/**
 * 配置模块
 */

// 测试模式
export const TEST_MODE = false;

// 设置要监控的平台
export const PLATFORMS = ["xiaohongshu", "youtube", "bilibili_live"];

// 中英文映射
export const CHINESE_MAP = {
    "weibo": "微博",
    "douyin": "抖音",
    "jrtt": "今日头条",
    "instagram": "Instagram",
    "xiaohongshu": "小红书",
    "bilibili": "哔哩哔哩",
    "youtube": "YouTube",
    "douyinlive": "抖音直播",
    "bilibili_live": "哔哩哔哩直播"
};

// 通知配置
export const NOTIFICATION_CONFIG = {
    // 息知通知
    xiZhi: {
        url: 'https://xizhi.qqoq.net/XZ74e57a9ecbbe01c5da6bb779ca9ee83b.send'
    },
    
    // 企业微信机器人
    qywx: {
        url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=632e6ef6-80d4-440f-86b7-3e606ddd6689"
    },
    
    // wxpusher 推送
    wxpusher: {
        url: 'https://wxpusher.zjiecode.com/api/send/message',
        appToken: 'AT_1PdjQtq793fVt8NSCeRgLiGUYvm5iFpI',
        topicIds: ['37489']
    }
};

// 平台配置
export const PLATFORM_CONFIG = {
    instagram: {
        name: "instagram",
        url: "https://www.instagram.com/icely.cheung/",
        api: "https://www.pixwox.com/api/posts?username=icely.cheung&userid=425118410"
    },
    
    xiaohongshu: {
        name: "xiaohongshu",
        api: "https://rsshub.email-once.com/xiaohongshu/user/5b42eba211be10463a17b765/notes"
    },
    
    bilibili: {
        name: "bilibili",
        api: "https://rsshub.160621.xyz/bilibili/user/dynamic/604845541?key=dvm42B9gYKEtpbpFtmeBavdB3o6tZ3GUzUN2DfLmnsPQZh7EWyYm"
    },
    
    bilibili_live: {
        name: "bilibili_live",
        url: "https://live.bilibili.com/24972546",
        api: "https://api.live.bilibili.com/room/v1/Room/get_info?room_id=24972546"
    },
    
    weibo: {
        name: "weibo",
        api: "https://m.weibo.cn/api/container/getIndex?type=uid&value=1770529042&containerid=1076031770529042"
    },
    
    youtube: {
        name: "youtube",
        api: "https://rsshub.email-once.com/youtube/channel/UCGldeUGQr6xwEoypnVsWXlA"
    },
    
    douyin: {
        name: "douyin",
        api: "https://www.douyin.com/aweme/v1/web/aweme/post/"
    },
    
    douyinlive: {
        name: "douyinlive",
        url: "https://live.douyin.com/************",
        api: "https://live.douyin.com/webcast/room/web/enter/"
    },
    
    jrtt: {
        name: "jrtt",
        api: "https://www.toutiao.com/api/pc/list/user/feed"
    }
};
