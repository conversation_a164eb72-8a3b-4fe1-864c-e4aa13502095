/**
 * 时间工具模块
 */

/**
 * 获取当前东八区时间
 * @returns {string} 格式化的时间字符串 (HH:MM:SS)
 */
export function getCurrentEast8Time() {
    // 获取当前时间
    const now = new Date();

    // 转换为东八区时间
    const offset = 8; // 东八区偏移量
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000); // 转换为 UTC 时间
    const east8Time = new Date(utc + (3600000 * offset)); // 转换为东八区时间

    // 只获取时间部分（HH:MM:SS）
    const timeString = east8Time.toLocaleTimeString('zh-CN', {
        hour12: false, // 使用 24 小时制
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    return timeString;
}

/**
 * 格式化时间戳为本地时间字符串
 * @param {number} timestamp - Unix时间戳（秒）
 * @returns {string} 格式化的时间字符串
 */
export function formatTimestamp(timestamp) {
    return new Date(timestamp * 1000)
        .toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
        .replace(/[\u4e00-\u9fa5]/g, '')
        .replace('T', ' ')
        .slice(0, 19);
}
