/**
 * 阿细社交媒体监控系统
 * 重构后的主程序入口
 */

"use strict";

import { appService } from './src/services/app.js';
import { httpServer } from './src/services/server.js';
import { TEST_MODE } from './src/config/index.js';
import { getCurrentEast8Time } from './src/utils/time.js';

// 声明日志时间
const logTime = getCurrentEast8Time();

console.log(`测试模式: ${TEST_MODE}`);
console.log(`${logTime} : 阿细社交媒体监控系统启动`);

/**
 * 主程序启动逻辑
 */
async function main() {
    try {
        // 启动定时监控
        appService.startScheduledMonitoring();

        // 启动HTTP服务器
        httpServer.start();

        console.log(`${logTime} : 系统启动完成`);
    } catch (error) {
        console.error('系统启动失败:', error);
        process.exit(1);
    }
}

// 启动应用
main();
