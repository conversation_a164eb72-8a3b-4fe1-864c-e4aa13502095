"use strict";
// 导入解析xml的依赖
import { parse } from "jsr:@libs/xml";

// 时间生成函数
function getCurrentEast8Time() {
    // 获取当前时间
    const now = new Date();

    // 转换为东八区时间
    const offset = 8; // 东八区偏移量
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000); // 转换为 UTC 时间
    const east8Time = new Date(utc + (3600000 * offset)); // 转换为东八区时间

    // 只获取时间部分（HH:MM:SS）
    const timeString = east8Time.toLocaleTimeString('zh-CN', {
        hour12: false, // 使用 24 小时制
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    return timeString;
}

// 声明日志时间
const logTime = getCurrentEast8Time();

// 测试模式
const TEST_MODE = false;
console.log(`测试模式:${TEST_MODE}`);

// 设置要监控的平台["weibo", "douyin", "jrtt", "instagram", "xiaohongshu", "bilibili", "youtube","douyinlive", "bilibili_live"]
let PLATFORMS = ["xiaohongshu", "youtube", "bilibili_live"];
console.log(`${logTime} : 开启监控${PLATFORMS.length}个平台:${PLATFORMS}`);

// 中英文映射
const chineseMap = {
    "weibo": "微博",
    "douyin": "抖音",
    "jrtt": "今日头条",
    "instagram": "Instagram",
    "xiaohongshu": "小红书",
    "bilibili": "哔哩哔哩",
    "youtube": "YouTube",
    "douyinlive": "抖音直播",
    "bilibili_live": "哔哩哔哩直播"
};

// 打开 KV
const kv = await Deno.openKv();

// 发送通知类
class sendMessage {
    constructor() { }

    // 息知通知
    async xiZhi(message) {
        await fetch('https://xizhi.qqoq.net/XZ74e57a9ecbbe01c5da6bb779ca9ee83b.send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(message)
        }).then(response => response.json())
            .then(data => console.log(`息知推送成功:${JSON.stringify(data)}`))
            .catch(error => console.error(`息知推送失败：${JSON.stringify(error)}`));
    }

    // 企业微信机器人
    async sendQywx(message) {
        await fetch("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=632e6ef6-80d4-440f-86b7-3e606ddd6689", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(message)
        }).then(response => response.json())
            .then(data => console.log(`${logTime} : 企业微信机器人发送成功:${JSON.stringify(data)}`))
            .catch(error => console.error(`${logTime} : 企业微信机器人发送失败：${JSON.stringify(error)}`));
    }

    // 企业微信机器人测试
    async sendQywxTest(message) {
        await fetch("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=59af0c44-8182-47c8-8a48-551fd4df66ca", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "msgtype": "text",
                "text": {
                    "content": message
                }
            })
        }).then(response => response.json())
            .then(data => console.log(`企业微信机器人测试发送成功:${JSON.stringify(data)}`))
            .catch(error => console.error(`企业微信机器人测试发送失败：${JSON.stringify(error)}`));
    }

    // wxpusher 推送
    async sendWxpusher(platform) {
        if (TEST_MODE) {
            return;
        }
        await fetch('https://wxpusher.zjiecode.com/api/send/message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                appToken: 'AT_1PdjQtq793fVt8NSCeRgLiGUYvm5iFpI',
                content: `不提供预览，请自行前往 ${chineseMap[platform]} 查看`,
                summary: `阿细的 ${chineseMap[platform]} 有新动态`,
                contentType: 1,
                topicIds: ['37489'],
                url: "",
                verifyPayType: 0
            })
        }).then(response => response.json())
            .then(data => console.log(`${logTime} : wxpusher 推送成功:${JSON.stringify(data)}`))
            .catch(error => console.error(`${logTime} : wxpusher 推送失败：${JSON.stringify(error)}`));
    }
}

// 对比数据类
class compareData {
    constructor() { }

    /**
     * 对比数据
     * @param {string} platform 
     * @param {object} data 
     * @returns {number} 是否有更新
     */
    static async main(platform, data) {
        console.log(`${logTime} : 对比数据:${platform}`);
        let oldData = await kv.get([platform]);
        if (!oldData.value || oldData.value === null) {
            console.log(`${logTime} : 数据不存在，初始化数据`);
            await kv.set([platform], data);
            return
        } else if (data[platform] != oldData.value[platform]) {
            // 更新数据
            console.log(`${logTime} : 对比数据结果：有更新`);
            await kv.set([platform, "old"], oldData.value);
            await kv.set([platform], data);
            return 818;
        } else {
            console.log(`${logTime} : 对比数据结果：无更新`);
            return 520;
        }
    }
}

// 各平台类
// Instagram
class CheckInstagram extends sendMessage {
    constructor() {
        super();
        this.init();
        this.name = "instagram";
    }

    async init() {
        const data = await this.fetchPixwox();
        const changes = await compareData.main(this.name, data);
        if (changes == 818) {
            await this.sendWxpusher(this.name);

            const message = {
                "msgtype": "news",
                "news": {
                    "articles": [
                        {
                            "title": chineseMap[this.name],
                            "description": `阿细的 ${chineseMap[this.name]} 有新动态啦`,
                            "url": "https://www.instagram.com/icely.cheung/",
                            "picurl": "https://api.960818.xyz/img/ax/index.php"
                        }
                    ]
                }
            };
            await this.sendQywx(message);
        }
    }

    // 通过www.pixwox.com获取数据内容
    async fetchPixwox() {
        try {
            const response = await fetch('https://www.pixwox.com/api/posts?username=icely.cheung&userid=425118410');
            const result = await response.json();
            console.log(`获取${this.name}数据成功:${result.posts.count}`);
            const data = {
                [this.name]: result.posts.count,
            };
            return data;
        } catch (error) {
            console.error(`${logTime} : 从Pixwox API获取${this.name}数据失败：${error}`);
            this.sendQywxTest(`${logTime} : 从Pixwox API获取${this.name}数据失败：${error}`);
            return {};
        }
    }
}

// 小红书
class CheckXiaohongshu extends sendMessage {
    constructor() {
        super();
        this.init();
        this.name = "xiaohongshu";
    }

    async init() {
        // 获取小红书数据内容
        const data = await this.fetchXh();
        if (Object.keys(data).length === 0) {
            console.log(`${logTime} : 获取${this.name}数据失败`);
            this.sendQywxTest(`${logTime} : 获取${this.name}数据失败`);
            return;
        }
        const changes = await compareData.main(this.name, data);

        if (changes == 818) {
            await this.sendWxpusher(this.name);

            const message = {
                "msgtype": "news",
                "news": {
                    "articles": [
                        {
                            "title": chineseMap[this.name],
                            "description": data["标题"],
                            "url": data["链接"],
                            "picurl": data["图片链接"]
                        }
                    ]
                }
            };
            await this.sendQywx(message);
        }
    }

    // 获取小红书数据内容
    async fetchXh() {
        try {
            // 发送请求
            const response = await fetch("https://rsshub.email-once.com/xiaohongshu/user/5b42eba211be10463a17b765/notes");
            // 解析数据
            let result = await response.text();
            // 判断是否返回html
            if (result.trim().startsWith('<html')) {
                console.log(`${this.name}返回数据为html，估计被限制了`);
                return {};
            }
            result = parse(result).rss.channel;
            // 解析数据
            const data = {
                [this.name]: result.item[2].title,
                "标题": result.item[2].title,
                "链接": result.item[2].link,
                "图片链接": result.item[2].description.match(/<img.*?src="(.*?)"/)[1],
            };
            console.log(`获取${this.name}数据成功:${JSON.stringify(data)}`);
            return data;
        } catch (error) {
            console.error(`${logTime} : 从小红书API获取数据失败：${error}`);
            this.sendQywxTest(`${logTime} : 从小红书API获取数据失败：${error}`);
            return {};
        }
    }
}

// 哔哩哔哩
class CheckBilibili extends sendMessage {
    constructor() {
        super();
        this.init();
        this.name = "bilibili";
    }

    async init() {
        // 获取哔哩哔哩数据内容
        const data = await this.fetchBb();
        if (Object.keys(data).length === 0) {
            console.log(`获取${this.name}数据失败`);
            this.sendQywxTest(`获取${this.name}数据失败`);
            return;
        }
        const changes = await compareData.main(this.name, data);

        if (changes == 818) {
            await this.sendWxpusher(this.name);

            const message = {
                "msgtype": "markdown",
                "markdown": {
                    "content": `阿细的 **${chineseMap[this.name]}** 有新动态`
                }
            };
            await this.sendQywx(message);
        }
    }

    // 获取哔哩哔哩数据内容
    async fetchBb() {
        try {
            const response = await fetch("https://rsshub.160621.xyz/bilibili/user/dynamic/604845541?key=dvm42B9gYKEtpbpFtmeBavdB3o6tZ3GUzUN2DfLmnsPQZh7EWyYm");
            // const response = await fetch("https://rsshub.email-once.com/bilibili/user/dynamic/604845541");
            let result = await response.text();
            if (result.trim().startsWith('<html')) {
                console.log(`${this.name}返回数据为html，估计被限制了`);
                return {};
            }
            result = parse(result).rss.channel;
            const data = {
                [this.name]: result.item[0].pubDate
            };
            console.log(`获取${this.name}数据成功:${JSON.stringify(data)}`);
            return data;
        } catch (error) {
            console.error(`${logTime} : 从Bilibili API获取数据失败：${error}`);
            this.sendQywxTest(`${logTime} : 从Bilibili API获取数据失败：${error}`);
            return {};
        }
    }
}

// 哔哩哔哩直播
class CheckBilibiliLive extends sendMessage {
    constructor() {
        super();
        this.init();
        this.name = "bilibili_live";
    }

    async init() {
        // 获取哔哩哔哩直播数据内容
        const data = await this.fetchBbLiveApi();
        if (Object.keys(data).length === 0) {
            console.log(`${logTime}：获取${this.name}数据失败`);
            this.sendQywxTest(`${logTime}：获取${this.name}数据失败`);
            return;
        }
        const oldData = await kv.get([this.name, "is_notified"]);
        if (data[this.name] == 1 && !oldData.value) {
            // 更新数据
            await kv.set([this.name, "is_notified"], "true");
            console.log(`${logTime}：阿细在${chineseMap[this.name]}啦，发送消息`);
            await this.sendWxpusher(this.name);

            const message = {
                "msgtype": "news",
                "news": {
                    "articles": [
                        {
                            "title": chineseMap[this.name],
                            "description": `阿细在${chineseMap[this.name]}啦，快来围观！`,
                            "url": "https://live.bilibili.com/24972546",
                            "picurl": data["图片链接"]
                        }
                    ]
                }
            };
            await this.sendQywx(message);
            return;
        } else if (data[this.name] == 0) {
            await kv.set([this.name, "is_notified"], "false");
        }
        return;
    }

    // 获取哔哩哔哩直播数据内容
    async fetchBbLiveApi() {
        try {
            const response = await fetch("https://api.live.bilibili.com/room/v1/Room/get_info?room_id=24972546");
            const result = await response.json();
            if (result.code !== 0) {
                console.log(`${logTime}：${this.name}请求api失败，估计被限制了`);
                return {};
            }
            const data = {
                [this.name]: result.data.live_status,
                "图片链接": result.data.user_cover,
            };
            return data;
        } catch (error) {
            console.error(`${logTime} : 从Bilibili API获取数据失败：${error}`);
            this.sendQywxTest(`${logTime} : 从Bilibili API获取数据失败：${error}`);
            return {};
        }
    }
}

// 微博
class CheckWeibo extends sendMessage {
    constructor() {
        super();
        this.init();
        this.name = "weibo";
    }

    async init() {
        // 获取微博数据内容
        const data = await this.fetchWeiboApi();
        const changes = await compareData.main(this.name, data);
        if (changes == 818) {
            await this.sendWxpusher(this.name);

            const message = {
                "msgtype": "markdown",
                "markdown": {
                    "content": `阿细的 **${chineseMap[this.name]}** 有新动态`
                }
            };
            await this.sendQywx(message);
        }
    }

    async fetchWeiboApi() {
        try {
            const response = await fetch("https://m.weibo.cn/api/container/getIndex?type=uid&value=1770529042&containerid=1076031770529042", {
                "headers": {
                    "accept": "application/json, text/plain, */*",
                    "mweibo-pwa": "1",
                    "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Microsoft Edge\";v=\"133\", \"Chromium\";v=\"133\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "x-requested-with": "XMLHttpRequest",
                    "x-xsrf-token": "b489df",
                    "Referer": "https://m.weibo.cn/u/1770529042",
                    "Referrer-Policy": "strict-origin-when-cross-origin"
                },
                "body": null,
                "method": "GET"
            });
            let result = await response.json();
            if (result.ok !== 1) {
                console.log(`${logTime}：${this.name}通过api获取数据失败，估计被限制了`);
                this.sendQywxTest(`${logTime}：${this.name}通过api获取数据失败，估计被限制了`);
                return;
            }
            result = result.data.cards;
            const data = {
                [this.name]: result[1].mblog.created_at,
            };
            console.log(`获取${this.name}数据成功:${JSON.stringify(data)}`);
            return data;
        } catch (error) {
            console.error(`${logTime} : 从微博API获取数据失败：${error}`);
            this.sendQywxTest(`${logTime} : 从微博API获取数据失败：${error}`);
            return {};
        }
    }
}

// youtube
class CheckYoutube extends sendMessage {
    constructor() {
        super();
        this.init();
        this.name = "youtube";
    }

    async init() {
        // 获取youtube数据内容
        const data = await this.fetchYt();
        if (Object.keys(data).length === 0) {
            console.log(`获取${this.name}数据失败`);
            this.sendQywxTest(`获取${this.name}数据失败`);
            return;
        }
        // 对比 kv 内容
        const changes = await compareData.main(this.name, data);

        if (changes == 818) {
            await this.sendWxpusher(this.name);

            const message = {
                "msgtype": "markdown",
                "markdown": {
                    "content": `阿细的 **${chineseMap[this.name]}** 有新动态`
                }
            };
            await this.sendQywx(message);
        }
    }

    // 获取youtube数据内容
    async fetchYt() {
        try {
            const response = await fetch("https://rsshub.email-once.com/youtube/channel/UCGldeUGQr6xwEoypnVsWXlA");
            let result = await response.text();
            if (result.trim().startsWith('<html')) {
                console.log(`${this.name}返回数据为html，估计被限制了`);
                return {};
            }
            result = parse(result).rss.channel;
            const data = {
                [this.name]: result.item[0].pubDate,
            };
            console.log(`获取${this.name}数据成功:${JSON.stringify(data)}`);
            return data;
        } catch (error) {
            console.error(`${logTime} : 从Youtube API获取数据失败：${error}`);
            this.sendQywxTest(`${logTime} : 从Youtube API获取数据失败：${error}`);
            return {};
        }
    }
}

// 抖音
class CheckDouyin extends sendMessage {
    constructor() {
        super();
        this.init();
        this.name = "douyin";
    }
    async init() {
        // 获取抖音数据内容
        const data = await this.fetchDy();
        if (Object.keys(data).length === 0) {
            console.log(`获取${this.name}数据失败`);
            this.sendQywxTest(`获取${this.name}数据失败`);
            return;
        }
        // 对比 kv 内容
        const changes = await compareData.main("douyin", data);

        if (changes == 818) {
            await this.sendWxpusher(this.name);

            const message = {
                "msgtype": "markdown",
                "markdown": {
                    "content": `阿细的 **${chineseMap[this.name]}** 有新动态！`
                }
            };
            await this.sendQywx(message);
        }
    }

    // 获取抖音数据内容
    async fetchDy() {
        try {
            const response = await fetch("https://www.douyin.com/aweme/v1/web/aweme/post/?device_platform=webapp&aid=6383&channel=channel_pc_web&sec_user_id=MS4wLjABAAAA4iJURTJXsUmAQ9-YzI1PAwmuhDcT08szbjSPil0vygM&max_cursor=0&locate_item_id=7384812466506599690&locate_query=false&show_live_replay_strategy=1&need_time_list=1&time_list_query=0&whale_cut_token=&cut_version=1&count=18&publish_video_strategy_type=2&from_user_page=1&update_version_code=170400&pc_client_type=1&pc_libra_divert=Windows&support_h265=1&support_dash=0&version_code=290100&version_name=29.1.0&cookie_enabled=true&screen_width=1552&screen_height=873&browser_language=zh-CN&browser_platform=Win32&browser_name=Edge&browser_version=*********&browser_online=true&engine_name=Blink&engine_version=*********&os_name=Windows&os_version=10&cpu_core_num=6&device_memory=8&platform=PC&downlink=10&effective_type=4g&round_trip_time=200&webid=7475598730408117786&uifid=3af258ad659545d9553f15cf32bb8a88df248991ebb865c20b5fa6f7dab6eb5462da455c4d5150d8b5a08efea81c1fa2dd7fdc6cc4ddb55081ed93dd999bec79276ab24d95cf93bd70aceb2551d61b2fba257dbfc6a14cae5536831415ce9965dd965c38a2d5f28e574168fe0c31de99&a_bogus=EJ4jDq7yxp5RKdFGmcGrC9%2FUpIolNTSy9tTxWx-THOu9T7lGvYPpENawcxu24uKTOYBswCIHkDeAbdnc%2F0X0ZFHkFmpkuBUymGQII0so%2Fq7VbBvgV1b8eJbEKi-FUWsPO%2FdAinEXU0lrIocfZHK2AFF9CAto58m8sre-dPWlHxg-g-JYVxd4eahP&verifyFp=verify_m7lhmmkp_5fZqgaEO_trUZ_4ott_BLeo_7Ab8NnjAGBut&fp=verify_m7lhmmkp_5fZqgaEO_trUZ_4ott_BLeo_7Ab8NnjAGBut", {
                "headers": {
                    "accept": "application/json, text/plain, */*",
                    "accept-language": "zh-CN,zh;q=0.9",
                    "cache-control": "no-cache",
                    "pragma": "no-cache",
                    "priority": "u=1, i",
                    "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Microsoft Edge\";v=\"133\", \"Chromium\";v=\"133\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "uifid": "3af258ad659545d9553f15cf32bb8a88df248991ebb865c20b5fa6f7dab6eb5462da455c4d5150d8b5a08efea81c1fa2dd7fdc6cc4ddb55081ed93dd999bec79276ab24d95cf93bd70aceb2551d61b2fba257dbfc6a14cae5536831415ce9965dd965c38a2d5f28e574168fe0c31de99",
                    "cookie": "__ac_nonce=067bea982007ba1ff211f; __ac_signature=_02B4Z6wo00f01Gw8YqwAAIDCiTVbXCvb3yhsHGYAAHy5ba; ttwid=1%7CW9m1sV9VuMyw4M2MBklOwIvs_1gyZIGFxKxiICMnIFo%7C1740548482%7C086de15b244896c9309053eed81bc18053836bd94ac0c7b9f7d3b1f91469f68a; UIFID_TEMP=3af258ad659545d9553f15cf32bb8a88df248991ebb865c20b5fa6f7dab6eb5462da455c4d5150d8b5a08efea81c1fa2dd7fdc6cc4ddb55081ed93dd999bec79276ab24d95cf93bd70aceb2551d61b2fba257dbfc6a14cae5536831415ce9965dd965c38a2d5f28e574168fe0c31de99; hevc_supported=true; IsDouyinActive=true; home_can_add_dy_2_desktop=%220%22; dy_swidth=1552; dy_sheight=873; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1552%2C%5C%22screen_height%5C%22%3A873%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A6%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A200%7D%22; csrf_session_id=dd42e9883c30dce5340b02ed663de029",
                    "Referer": "https://www.douyin.com/user/MS4wLjABAAAA4iJURTJXsUmAQ9-YzI1PAwmuhDcT08szbjSPil0vygM?vid=7384812466506599690",
                    "Referrer-Policy": "strict-origin-when-cross-origin",
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
                },
                "body": null,
                "method": "GET"
            });
            const res = await response.json();
            const result = res.aweme_list[3];
            // console.log(`抖音API返回数据:${JSON.stringify(result)}`);
            const data = {
                [this.name]: new Date(result.create_time * 1000)
                    .toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
                    .replace(/[\u4e00-\u9fa5]/g, '')
                    .replace('T', ' ')
                    .slice(0, 19),
            };
            console.log(`获取${this.name}数据成功:${JSON.stringify(data)}`);
            return data;
        } catch (error) {
            console.error(`${logTime} : 从抖音API获取数据失败：${error}`);
            this.sendQywxTest(`${logTime} : 从抖音API获取数据失败：${error}`);
            return {};
        }
    }
}

// 抖音直播
class CheckDouyinLive extends sendMessage {
    constructor() {
        super();
        this.init();
        this.name = "douyinlive";
    }

    async init() {
        let data = await this.fetchLiveApi();
        const oldData = await kv.get([this.name, "is_notified"]);
        if (oldData === null) {
            await kv.set([this.name, "is_notified"], false);
        }
        if (data[this.name] == 2 && !oldData.value) {
            // 更新数据
            await kv.set([this.name, "is_notified"], "true");
            console.log(`阿细在抖音直播啦，发送消息`);

            const message = {
                "msgtype": "news",
                "news": {
                    "articles": [
                        {
                            "title": chineseMap[this.name],
                            "description": `阿细在${chineseMap[this.name]}直播啦，快来围观！`,
                            "url": "https://live.douyin.com/713643052823",
                            "picurl": "https://api.960818.xyz/img/ax/index.php"
                        }
                    ]
                }
            };
            await this.sendQywx(message);
            return;
        } else if (data[this.name] == 4) {
            await kv.set([this.name, "is_notified"], "false");
        } else {
            return;
        }
    }

    // 通过抖音api获取抖音直播数据内容
    async fetchLiveApi() {
        try {
            const response = await fetch("https://live.douyin.com/webcast/room/web/enter/?aid=6383&app_name=douyin_web&live_id=1&device_platform=web&language=zh-CN&enter_from=page_refresh&cookie_enabled=true&screen_width=1552&screen_height=873&browser_language=zh-CN&browser_platform=Win32&browser_name=Edge&browser_version=*********&web_rid=713643052823&room_id_str=7470146013328624418&enter_source=&is_need_double_stream=false&insert_task_id=&live_reason=&a_bogus=dj4nktXwEZ%2FRad%2FGYKG1Cn5Uf0ylNsSy3sixSHVuHNPAT7zTLSPxgOCznxu14KAiaRBsioVHBDelYxdcQIXw1ZHkzmkvSFift025VX0L2qwXaUikgHDDehDFKwBC8mJwe%2F99ilf5Xs0N1VdAINV%2FAdlGH5FH-bEdSrpep%2FEynDc03B6TV92ICrYWUwD%3D", {
                "headers": {
                    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                    "cache-control": "no-cache",
                    "pragma": "no-cache",
                    "priority": "u=0, i",
                    "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Microsoft Edge\";v=\"133\", \"Chromium\";v=\"133\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "document",
                    "sec-fetch-mode": "navigate",
                    "sec-fetch-site": "none",
                    "sec-fetch-user": "?1",
                    "sec-gpc": "1",
                    "upgrade-insecure-requests": "1",
                    "cookie": "xgplayer_user_id=548922642316; live_use_vvc=%22false%22; hevc_supported=true; UIFID_TEMP=c3109cf8eab4507640f022360c5ce002c7035d0857c7085fdeb180d1661fca19c4e53e0d34762335ad1c35bb6b953d9375b2c04a7ae6bebc31fca630c2289a92b58609360c4f8dc899db6a1d5f4ae9ac96b57d48d07514364cafae671f43305807c831b9b0aabd96d0d2389d19b4ac3e; UIFID=c3109cf8eab4507640f022360c5ce002c7035d0857c7085fdeb180d1661fca19b7605030b444423ff3525c68b7a4b37273864e9e0b643eeae627c95c699a4656b94b7ce2bbef2a455e234961cad69dc3bc6c7e2c8d1f1bd067f79db80f91c2750a9038d3bb1132668b4d5e0243f86315325212955c90d014394a5bb1d2b29cbb2139b48338621d0e815bae9f699e9b86f2db326b15b39fe5e2e5f726f41c8d41; bd_ticket_guard_client_web_domain=2; d_ticket=6633613373be238632143c78eb86ec8c033d5; passport_assist_user=Cj2_gpELHbGlehPNLKg6M0qPJ6iZDWtSWZ8UjmE5SDAEAbqv0bhSLZnD9_BZn84polzAtvd8DZzpMDNMRrBdGkoKPOgnoNz-cm4XHKS2qfdveCAU1o5p1s0rl-yR7s-6ZKJ6ImT5un4ImKbQvtOBkpdtOW08sWgbRpvw-4sUrRDU3N0NGImv1lQgASIBA-ymVM4%3D; uid_tt=7e96da6ea85d2440106d3cb275f774bd; uid_tt_ss=7e96da6ea85d2440106d3cb275f774bd; sid_tt=d67ddd9c174cb877d62354905ab4006d; sessionid=d67ddd9c174cb877d62354905ab4006d; sessionid_ss=d67ddd9c174cb877d62354905ab4006d; is_staff_user=false; _bd_ticket_crypt_cookie=47d00ded0a068e2f0de4d1812ca16fea; store-region=cn-gd; store-region-src=uid; my_rd=2; fpk1=U2FsdGVkX1+ymz03MosiwCj5ebO7sTgH4iKjoQgDYJsstClbYiyQlZ5VweohI1zWC8mVva2+ijP3fXetSW8Vmw==; fpk2=e2dd8fac9c214f2e57aa0dea655ff030; ttwid=1%7C4Wp8fqI8tXNcDlNdR8SO0B-EvUF8ksuggm8C-KkmtaA%7C1735652835%7Cd8d1e9bda8e60efedd93f778884301a91ec959ed119c8956808d11362b71f00a; __security_mc_1_s_sdk_crypt_sdk=f9940e15-4bb3-bae2; __security_mc_1_s_sdk_cert_key=0f9c26ac-47c4-bd1d; __security_mc_1_s_sdk_sign_data_key_web_protect=bc476d67-4bca-9cbe; theme=%22dark%22; manual_theme=%22dark%22; passport_csrf_token=fb034d59eb208b1153fa62ae81c43200; passport_csrf_token_default=fb034d59eb208b1153fa62ae81c43200; is_dash_user=1; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Atrue%2C%22volume%22%3A0.5%7D; publish_badge_show_info=%220%2C0%2C0%2C1739943732589%22; sid_guard=d67ddd9c174cb877d62354905ab4006d%7C1739943733%7C5184000%7CSun%2C+20-Apr-2025+05%3A42%3A13+GMT; sid_ucp_v1=1.0.0-KDI5NDc5MTgzMjgzYzQ5YjVkZDk4NzI0NTdlYzQ5ZWQ4NGE2OTRlZTAKGQjcu7b_kgMQtd7VvQYY7zEgDDgCQPEHSAQaAmxmIiBkNjdkZGQ5YzE3NGNiODc3ZDYyMzU0OTA1YWI0MDA2ZA; ssid_ucp_v1=1.0.0-KDI5NDc5MTgzMjgzYzQ5YjVkZDk4NzI0NTdlYzQ5ZWQ4NGE2OTRlZTAKGQjcu7b_kgMQtd7VvQYY7zEgDDgCQPEHSAQaAmxmIiBkNjdkZGQ5YzE3NGNiODc3ZDYyMzU0OTA1YWI0MDA2ZA; WallpaperGuide=%7B%22showTime%22%3A1739947489479%2C%22closeTime%22%3A0%2C%22showCount%22%3A1%2C%22cursor1%22%3A10%2C%22cursor2%22%3A2%7D; download_guide=%223%2F20250219%2F0%22; SelfTabRedDotControl=%5B%7B%22id%22%3A%227343913167581366291%22%2C%22u%22%3A213%2C%22c%22%3A213%7D%2C%7B%22id%22%3A%226778427804661843982%22%2C%22u%22%3A92%2C%22c%22%3A92%7D%2C%7B%22id%22%3A%227340472706682341410%22%2C%22u%22%3A18%2C%22c%22%3A18%7D%5D; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1552%2C%5C%22screen_height%5C%22%3A873%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A6%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A0%7D%22; strategyABtestKey=%************.458%22; biz_trace_id=e42c7650; home_can_add_dy_2_desktop=%221%22; __ac_signature=_02B4Z6wo00f01pnmYCgAAIDAfO9Z2G3SiDqZxmSAAMHN30; __live_version__=%221.1.2.8430%22; xg_device_score=7.29597176524887; has_avx2=null; device_web_cpu_core=6; device_web_memory_size=8; csrf_session_id=581eab18b5998e2fef90b761faf8adfd; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCTXc2MzNtN1ZzSGh2cG1CUVZMa3RPRzlhQm1lNVEwL2dFSDFPWnQwVEJGT0xIMERKd3kzK0t5bU1RWGIxOUZGSG5hQzlhdStuNy96NStpZGJ2bm0zNHM9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D; odin_tt=fe6a90f6ed9268dff306a2c1c0d129a979ec43d78d8267aa008d0144b8b78911bca7ce792fdfd4154da504c2fe19b3ba; live_can_add_dy_2_desktop=%221%22; passport_fe_beating_status=false; IsDouyinActive=false"
                },
                "referrerPolicy": "strict-origin-when-cross-origin",
                "body": null,
                "method": "GET"
            });
            const result = await response.json();
            if (!result.data.data[0].id_str) {
                console.log(`${logTime}: ${this.name}通过api获取数据失败，估计被限制了`);
                return {};
            }
            const data = {
                [this.name]: result.data.data[0].status,
            };
            console.log(`通过抖音API获取抖音直播数据结果：${JSON.stringify(data)}`);
            return data;
        } catch (e) {
            console.log(`${this.name}通过api获取数据失败`);
            this.sendQywxTest(`${this.name}通过api获取数据失败`);
            return {};
        }
    }
}

// 今日头条
class CheckJRTT extends sendMessage {
    constructor() {
        super();
        this.init();
        this.name = "jrtt";
    }

    async init() {
        const data = await this.fetchJrttApi();
        if (Object.keys(data).length === 0) {
            console.log(`获取${this.name}数据失败`);
            this.sendQywxTest(`获取${this.name}数据失败`);
            return;
        }
        // 对比 kv 内容
        const changes = await compareData.main("jrtt", data);

        // 判断是否需要发送消息
        if (changes == 818) {
            await this.sendWxpusher(this.name);

            const message = {
                "msgtype": "markdown",
                "markdown": {
                    "content": `阿细 **${chineseMap[this.name]}** 有新动态`
                }
            };
            await this.sendQywx(message);
        }
    }

    // 通过接口获取今日头条数据
    async fetchJrttApi() {
        try {
            console.log("通过接口获取今日头条数据");
            // 发送请求
            const response = await fetch("https://www.toutiao.com/api/pc/list/user/feed?category=profile_all&token=MS4wLjABAAAAFJ-UpRm-b-wkM_qWY8AMxB6ZbeXRSCfC1CBI6MBpx10&max_behot_time=0&entrance_gid=&aid=24&app_name=toutiao_web&a_bogus=OX80QfLkdDViDDWV54nLfY3qVXN3Y2EV0t9bMDhqexVP1y39HMYF9exoNrTvwcEjx4%2FhIeYjy4hbYrnhrQ270Hwf9WXE%2F25gmDSkKl5Q5xSSs1X9CLXgJ00Nmkt5tFn2RvMWrOX%2Fow-HFb82ldAJ5kIlO62-zo0%2F9-j%3D", {
                "headers": {
                    "accept": "application/json, text/plain, */*",
                    "accept-language": "zh-CN,zh;q=0.9",
                    "cache-control": "no-cache",
                    "pragma": "no-cache",
                    "priority": "u=1, i",
                    "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Microsoft Edge\";v=\"133\", \"Chromium\";v=\"133\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "sec-gpc": "1",
                    "cookie": "__ac_nonce=067b452570058a402d798; __ac_signature=_02B4Z6wo00f01RxNETwAAIDD-UQozvTriJkcbRWAACCp8b; tt_webid=7472688133131683340; gfkadpd=24,6457; ttcid=396a59483eb242b6824c534ea451f70020; ttwid=1%7CXPQoFlzKPks9ISE1Wzj_Sl4hHbdYEbkp6eslnlFftIQ%7C1739870809%7C218aac0ad0a3174df456eb91f17900c9b3fada0b188c6fa0eaaf2789c9569660; local_city_cache=%E4%BD%9B%E5%B1%B1; csrftoken=bd2d5e93431d885bfcc350b6465e72ae",
                    "Referer": "https://www.toutiao.com/c/user/token/MS4wLjABAAAAFJ-UpRm-b-wkM_qWY8AMxB6ZbeXRSCfC1CBI6MBpx10/?wid=1739870806885",
                    "Referrer-Policy": "strict-origin-when-cross-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
                },
                "body": null,
                "method": "GET"
            });
            const result = await response.json();
            // 解析数据
            if (result.message !== 'success') {
                console.log(`${logTime}：${this.name}接口返回数据异常`);
                this.sendQywxTest(`${logTime}：${this.name}接口返回数据异常`);
                return {};
            }
            const data = {
                [this.name]: new Date(result.data[0].create_time * 1000)
                    .toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
                    .replace(/[\u4e00-\u9fa5]/g, '')
                    .replace('T', ' ')
                    .slice(0, 19),
            };
            console.log(`今日头条数据：${JSON.stringify(data)}`);
            return data;
        } catch (e) {
            console.log(`${this.name}接口获取数据失败`);
            this.sendQywxTest(`${this.name}接口获取数据失败`);
            return {};
        }
    }
}

// 根据平台匹配实例化类
const PLATFORM_MAP = {
    "instagram": CheckInstagram,
    "xiaohongshu": CheckXiaohongshu,
    "douyin": CheckDouyin,
    "weibo": CheckWeibo,
    "bilibili": CheckBilibili,
    "youtube": CheckYoutube,
    "jrtt": CheckJRTT,
    "douyinlive": CheckDouyinLive,
    "bilibili_live": CheckBilibiliLive,
};

// 主函数
async function main() {
    for (const i of PLATFORMS) {
        try {
            await new PLATFORM_MAP[i]();
            console.log(`实例化${i}完成`);
        }
        catch (e) {
            this.sendMsg({
                msgtype: "text",
                text: {
                    content: `实例化${i}失败，请检查日志`
                }
            })
            console.error(`实例化${i}失败`, e);
        }
    }
}

// 删除kv数据
async function deleteData() {
    PLATFORMS.forEach(async (platform) => {
        await kv.delete([platform]);
        console.log(`${platform}数据已删除`);
    });
    return new Response("删除成功", {
        headers: {
            'Content-Type': 'text/plain; charset=utf-8'
        }
    });
}

// 日常报告函数
async function data() {
    // 获取全部数据，并格式化返回
    let res = [];
    for (const i of PLATFORMS) {
        data = await kv.get([i]);
        res.push({
            platform: chineseMap[i],
            data: data.value,
        });
    }
    res = JSON.stringify(res, null, 2);
    // 发送消息
    const sendMsg = new sendMessage();
    sendMsg.sendQywxTest(res);
    return new Response(res, {
        headers: {
            'Content-Type': 'application/json; charset=utf-8'
        }
    });
}

// 手动执行
async function manualGetData(url) {
    const platform = url.pathname.split('/')[1];
    await new PLATFORM_MAP[platform]();
    return new Response(`手动执行检查${platform}完成`, {
        headers: {
            'Content-Type': 'text/plain; charset=utf-8'
        }
    });
}

// 每五分钟执行一次
Deno.cron('Check', '*/4 * * * *', () => {
    // 生成30到90随机秒数
    const randomSeconds = Math.floor(Math.random() * 60) + 30;
    console.log(`随机等待${randomSeconds}秒`);
    setTimeout(async () => {
        await main();
    }, randomSeconds * 1000);
});


// 路径处理map
const pathHandler = new Map();
pathHandler.set('/xiaohongshu', manualGetData);
pathHandler.set('/weibo', manualGetData);
pathHandler.set('/youtube', manualGetData);
pathHandler.set('/instagram', manualGetData);
pathHandler.set('/jrtt', manualGetData);
pathHandler.set('/douyin', manualGetData);
pathHandler.set('/douyinlive', manualGetData);
pathHandler.set('/bilibili', manualGetData);
pathHandler.set('/bilibili_live', manualGetData);

pathHandler.set('/data', data);
pathHandler.set('/favicon.ico', () => new Response(null, { status: 204 }));
pathHandler.set('/delete', deleteData);

// http server
Deno.serve(async (req) => {
    const url = new URL(req.url);
    console.log(`接收到请求：${url.pathname}`);
    const handler = pathHandler.get(url.pathname);
    if (handler) {
        return handler(url);
    } else {
        return new Response("这里没啥好看的", {
            headers: {
                'Content-Type': 'text/plain; charset=utf-8'
            }
        });
    }
});