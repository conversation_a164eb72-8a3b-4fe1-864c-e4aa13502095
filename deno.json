{"name": "ax-check", "version": "2.0.0", "description": "阿细社交媒体监控系统 - 重构版", "main": "main.js", "scripts": {"start": "deno run --allow-all main.js", "test": "deno run --allow-all test.js", "dev": "deno run --allow-all --watch main.js"}, "imports": {"@libs/xml": "jsr:@libs/xml@^7.0.0"}, "tasks": {"start": "deno run --allow-all main.js", "test": "deno run --allow-all test.js", "dev": "deno run --allow-all --watch main.js"}, "compilerOptions": {"allowJs": true, "lib": ["deno.window"], "strict": true}, "fmt": {"files": {"include": ["src/", "main.js", "test.js"]}, "options": {"useTabs": false, "lineWidth": 100, "indentWidth": 4, "semiColons": true, "singleQuote": true, "proseWrap": "preserve"}}, "lint": {"files": {"include": ["src/", "main.js", "test.js"]}}}