# 存储配置示例

本文档展示了如何在不同部署环境中配置存储系统。

## 🏗️ 存储架构概览

新的存储系统采用适配器模式，支持多种存储后端：

- **Deno KV** - Deno 原生键值存储（推荐用于 Deno Deploy）
- **Cloudflare KV** - Cloudflare Workers 键值存储
- **Memory** - 内存存储（用于开发和测试）
- **Auto** - 自动选择最佳存储类型

## 📋 配置方式

### 1. 环境变量配置

```bash
# 设置存储类型
export STORAGE_TYPE=deno-kv

# Cloudflare KV 配置（仅在使用 Cloudflare Workers 时需要）
export CF_KV_NAMESPACE=MY_KV_NAMESPACE
```

### 2. 代码配置

在 `src/config/index.js` 中修改：

```javascript
export const STORAGE_CONFIG = {
    type: 'deno-kv',  // 或 'cloudflare-kv', 'memory', 'auto'
    cloudflare: {
        namespace: 'MY_KV_NAMESPACE'
    }
};
```

## 🚀 部署环境配置

### Deno Deploy

```bash
# 设置环境变量
export STORAGE_TYPE=deno-kv

# 或者使用自动检测
export STORAGE_TYPE=auto
```

Deno Deploy 会自动提供 `Deno.openKv()` 功能。

### Cloudflare Workers

1. 在 Cloudflare Dashboard 中创建 KV namespace
2. 在 `wrangler.toml` 中绑定：

```toml
[[kv_namespaces]]
binding = "MY_KV_NAMESPACE"
id = "your-kv-namespace-id"
```

3. 设置环境变量：

```bash
export STORAGE_TYPE=cloudflare-kv
export CF_KV_NAMESPACE=MY_KV_NAMESPACE
```

### 本地开发

```bash
# 使用内存存储
export STORAGE_TYPE=memory

# 或者让系统自动选择
export STORAGE_TYPE=auto
```

### Docker 部署

```dockerfile
# Dockerfile
FROM denoland/deno:alpine

# 设置存储类型
ENV STORAGE_TYPE=memory

COPY . /app
WORKDIR /app

CMD ["deno", "run", "--allow-all", "main.js"]
```

### Vercel Edge Functions

```bash
# 使用内存存储（Vercel Edge Runtime 不支持持久化存储）
export STORAGE_TYPE=memory
```

## 🔧 高级配置

### 自定义存储适配器

如果需要支持其他存储后端，可以创建自定义适配器：

```javascript
// src/storage/custom.js
import { BaseStorageAdapter } from './base.js';

export class CustomAdapter extends BaseStorageAdapter {
    async init() {
        // 初始化逻辑
        return true;
    }

    async get(key) {
        // 获取数据逻辑
    }

    async set(key, value) {
        // 设置数据逻辑
    }

    async delete(key) {
        // 删除数据逻辑
    }

    getType() {
        return 'Custom Storage';
    }
}
```

然后在工厂类中注册：

```javascript
// src/storage/factory.js
import { CustomAdapter } from './custom.js';

export const STORAGE_TYPES = {
    // ... 现有类型
    CUSTOM: 'custom'
};

// 在 createAdapter 方法中添加
case STORAGE_TYPES.CUSTOM:
    return new CustomAdapter();
```

### 存储迁移

如果需要从一种存储类型迁移到另一种：

```javascript
// 迁移脚本示例
import { StorageFactory, STORAGE_TYPES } from './src/storage/factory.js';

async function migrateStorage(fromType, toType) {
    const sourceAdapter = StorageFactory.createAdapter(fromType);
    const targetAdapter = StorageFactory.createAdapter(toType);
    
    await sourceAdapter.init();
    await targetAdapter.init();
    
    // 实现数据迁移逻辑
    // 注意：需要根据具体的存储适配器实现遍历功能
    
    await sourceAdapter.cleanup();
    await targetAdapter.cleanup();
}
```

## 🧪 测试存储配置

使用提供的测试工具验证存储配置：

```bash
# 运行存储测试套件
deno run --allow-all test-storage.js

# 运行基本系统测试
deno run --allow-all test.js
```

## 📊 性能考虑

不同存储类型的性能特点：

| 存储类型 | 读取性能 | 写入性能 | 持久化 | 适用场景 |
|---------|---------|---------|--------|----------|
| Deno KV | 高 | 高 | ✅ | 生产环境 |
| Cloudflare KV | 高 | 中 | ✅ | Edge 计算 |
| Memory | 极高 | 极高 | ❌ | 开发测试 |

## 🔍 故障排除

### 常见问题

1. **Deno KV 不可用**
   - 确保使用 Deno 1.32+ 版本
   - 检查是否在支持的环境中运行

2. **Cloudflare KV 连接失败**
   - 验证 KV namespace 绑定
   - 检查环境变量配置

3. **内存存储数据丢失**
   - 这是正常行为，内存存储不持久化
   - 考虑切换到持久化存储

### 调试命令

```bash
# 检查存储环境
deno run --allow-all -e "
import { StorageManager } from './src/utils/storage-manager.js';
await StorageManager.printEnvironmentReport();
"

# 测试特定存储类型
deno run --allow-all -e "
import { StorageManager } from './src/utils/storage-manager.js';
await StorageManager.testAdapter('memory');
"
```

## 📝 最佳实践

1. **生产环境**：使用 `deno-kv` 或 `cloudflare-kv`
2. **开发环境**：使用 `memory` 或 `auto`
3. **CI/CD**：使用 `memory` 进行测试
4. **监控**：定期检查存储健康状态
5. **备份**：为重要数据实施备份策略
