/**
 * Instagram 平台检查器
 */

import { BasePlatformChecker } from './base.js';
import { PLATFORM_CONFIG } from '../config/index.js';

/**
 * Instagram 检查器类
 */
export class InstagramChecker extends BasePlatformChecker {
    constructor() {
        super("Instagram");
        this.config = PLATFORM_CONFIG["Instagram"];
    }

    /**
     * 初始化检查器
     */
    async init() {
        const data = await this.fetchData();
        const hasUpdate = await this.handleDataUpdate(data);
        
        if (hasUpdate) {
            const message = this.createNewsMessage(
                this.name,
                `阿细的 ${this.name} 有新动态啦`,
                this.config.url,
                "https://api.960818.xyz/img/ax/index.php"
            );
            await this.sendQywx(message);
        }
    }

    /**
     * 通过Pixwox API获取Instagram数据
     * @returns {Promise<object>} Instagram数据
     */
    async fetchData() {
        try {
            const response = await fetch(this.config.api);
            const result = await response.json();
            
            this.logSuccessfulFetch({ count: result.posts.count });
            
            const data = {
                [this.name]: result.posts.count,
            };
            
            return data;
        } catch (error) {
            await this.handleApiError(error, 'Pixwox API');
            return {};
        }
    }
}
