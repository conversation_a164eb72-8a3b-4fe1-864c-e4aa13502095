/**
 * 应用服务模块
 */

import { PLATFORMS, CHINESE_MAP } from '../config/index.js';
import { getPlatformChecker } from '../checkers/index.js';
import { storageService } from './storage.js';
import { NotificationService } from './notification.js';
import { getCurrentEast8Time } from '../utils/time.js';

/**
 * 应用服务类
 */
export class AppService {
    constructor() {
        this.notificationService = new NotificationService();
        this.logTime = getCurrentEast8Time();
    }

    /**
     * 主函数 - 检查所有配置的平台
     */
    async checkAllPlatforms() {
        console.log(`${this.logTime} : 开启监控${PLATFORMS.length}个平台:${PLATFORMS}`);
        
        for (const platform of PLATFORMS) {
            try {
                const checker = getPlatformChecker(platform);
                await checker.run();
                console.log(`实例化${platform}完成`);
            } catch (error) {
                const errorMessage = `实例化${platform}失败，请检查日志`;
                console.error(errorMessage, error);
                await this.notificationService.sendQywxTest(errorMessage);
            }
        }
    }

    /**
     * 手动检查指定平台
     * @param {string} platform - 平台名称
     */
    async checkSinglePlatform(platform) {
        try {
            const checker = getPlatformChecker(platform);
            await checker.run();
            return `手动执行检查${platform}完成`;
        } catch (error) {
            const errorMessage = `手动检查${platform}失败：${error.message}`;
            console.error(errorMessage, error);
            await this.notificationService.sendQywxTest(errorMessage);
            throw error;
        }
    }

    /**
     * 获取所有平台数据报告
     * @returns {Promise<Array>} 平台数据报告
     */
    async getDataReport() {
        const results = await storageService.getAllPlatformData(PLATFORMS);
        
        // 格式化数据，添加中文名称
        const formattedResults = results.map(item => ({
            platform: CHINESE_MAP[item.platform] || item.platform,
            data: item.data,
        }));

        // 发送报告到测试群
        const reportText = JSON.stringify(formattedResults, null, 2);
        await this.notificationService.sendQywxTest(reportText);
        
        return formattedResults;
    }

    /**
     * 删除所有平台数据
     */
    async deleteAllData() {
        await storageService.deleteAllPlatformData(PLATFORMS);
        return "删除成功";
    }

    /**
     * 创建定时任务
     * @param {number} intervalMinutes - 间隔分钟数
     * @param {Function} callback - 回调函数
     */
    createIntervalJob(intervalMinutes, callback) {
        const intervalMs = intervalMinutes * 60 * 1000;

        setInterval(() => {
            // 生成30到90随机秒数
            const randomSeconds = Math.floor(Math.random() * 60) + 30;
            console.log(`随机等待${randomSeconds}秒`);

            setTimeout(async () => {
                try {
                    await callback();
                } catch (error) {
                    console.error('定时任务执行失败:', error);
                    await this.notificationService.sendQywxTest(`定时任务执行失败: ${error.message}`);
                }
            }, randomSeconds * 1000);
        }, intervalMs);
    }

    /**
     * 启动定时监控
     */
    startScheduledMonitoring() {
        // 每4分钟执行一次
        this.createIntervalJob(4, () => this.checkAllPlatforms());
        console.log('定时监控已启动，每4分钟执行一次检查');
    }
}

// 创建单例实例
export const appService = new AppService();
