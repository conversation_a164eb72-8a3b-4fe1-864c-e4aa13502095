/**
 * 小红书平台检查器
 */

import { parse } from "jsr:@libs/xml";
import { BasePlatformChecker } from './base.js';
import { PLATFORM_CONFIG } from '../config/index.js';

/**
 * 小红书检查器类
 */
export class XiaohongshuChecker extends BasePlatformChecker {
    constructor() {
        super("xiaohongshu");
        this.config = PLATFORM_CONFIG.xiaohongshu;
    }

    /**
     * 初始化检查器
     */
    async init() {
        const data = await this.fetchData();
        const hasUpdate = await this.handleDataUpdate(data);
        
        if (hasUpdate) {
            const message = this.createNewsMessage(
                this.name,
                data["标题"],
                data["链接"],
                data["图片链接"]
            );
            await this.sendQywx(message);
        }
    }

    /**
     * 获取小红书数据
     * @returns {Promise<object>} 小红书数据
     */
    async fetchData() {
        try {
            const response = await fetch(this.config.api);
            let result = await response.text();
            
            // 判断是否返回html
            if (this.isHtmlResponse(result)) {
                console.log(`${this.name}返回数据为html，估计被限制了`);
                return {};
            }
            
            result = parse(result).rss.channel;
            
            // 解析数据
            const data = {
                [this.name]: result.item[2].title,
                "标题": result.item[2].title,
                "链接": result.item[2].link,
                "图片链接": result.item[2].description.match(/<img.*?src="(.*?)"/)[1],
            };
            
            this.logSuccessfulFetch(data);
            return data;
        } catch (error) {
            await this.handleApiError(error, '小红书API');
            return {};
        }
    }
}
