/**
 * 数据存储服务模块
 */

import { getCurrentEast8Time } from '../utils/time.js';

/**
 * 数据存储服务类
 */
export class StorageService {
    constructor() {
        this.storage = new Map(); // 使用内存存储替代KV
        this.logTime = getCurrentEast8Time();
    }

    /**
     * 初始化存储
     */
    async init() {
        // 内存存储不需要初始化
        return this.storage;
    }

    /**
     * 将键数组转换为字符串键
     * @param {Array} key - 键数组
     * @returns {string} 字符串键
     */
    keyToString(key) {
        return Array.isArray(key) ? key.join(':') : key;
    }

    /**
     * 获取数据
     * @param {Array} key - 键数组
     * @returns {Promise<any>} 数据值
     */
    async get(key) {
        const keyStr = this.keyToString(key);
        const value = this.storage.get(keyStr);
        return { value: value || null };
    }

    /**
     * 设置数据
     * @param {Array} key - 键数组
     * @param {any} value - 值
     */
    async set(key, value) {
        const keyStr = this.keyToString(key);
        this.storage.set(keyStr, value);
        return true;
    }

    /**
     * 删除数据
     * @param {Array} key - 键数组
     */
    async delete(key) {
        const keyStr = this.keyToString(key);
        return this.storage.delete(keyStr);
    }

    /**
     * 对比数据
     * @param {string} platform - 平台名称
     * @param {object} data - 新数据
     * @returns {Promise<number>} 818表示有更新，520表示无更新，undefined表示初始化
     */
    async compareData(platform, data) {
        console.log(`${this.logTime} : 对比数据:${platform}`);
        
        const oldData = await this.get([platform]);
        
        if (!oldData.value || oldData.value === null) {
            console.log(`${this.logTime} : 数据不存在，初始化数据`);
            await this.set([platform], data);
            return;
        } else if (data[platform] != oldData.value[platform]) {
            // 更新数据
            console.log(`${this.logTime} : 对比数据结果：有更新`);
            await this.set([platform, "old"], oldData.value);
            await this.set([platform], data);
            return 818;
        } else {
            console.log(`${this.logTime} : 对比数据结果：无更新`);
            return 520;
        }
    }

    /**
     * 检查直播状态并处理通知
     * @param {string} platform - 平台名称
     * @param {object} data - 数据
     * @param {number} liveStatus - 直播状态（1或2表示直播中）
     * @returns {Promise<boolean>} 是否需要发送通知
     */
    async checkLiveStatus(platform, data, liveStatus) {
        const oldData = await this.get([platform, "is_notified"]);
        
        if (data[platform] == liveStatus && !oldData.value) {
            // 更新数据
            await this.set([platform, "is_notified"], "true");
            console.log(`${this.logTime}：阿细在${platform}直播啦，发送消息`);
            return true;
        } else if (data[platform] != liveStatus) {
            await this.set([platform, "is_notified"], "false");
        }
        
        return false;
    }

    /**
     * 获取所有平台数据
     * @param {Array<string>} platforms - 平台列表
     * @returns {Promise<Array>} 所有平台数据
     */
    async getAllPlatformData(platforms) {
        const results = [];
        
        for (const platform of platforms) {
            const data = await this.get([platform]);
            results.push({
                platform,
                data: data.value,
            });
        }
        
        return results;
    }

    /**
     * 删除所有平台数据
     * @param {Array<string>} platforms - 平台列表
     */
    async deleteAllPlatformData(platforms) {
        for (const platform of platforms) {
            await this.delete([platform]);
            console.log(`${platform}数据已删除`);
        }
    }
}

// 创建单例实例
export const storageService = new StorageService();
