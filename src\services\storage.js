/**
 * 数据存储服务模块
 */

import { getCurrentEast8Time } from '../utils/time.js';
import { StorageFactory, STORAGE_TYPES } from '../storage/factory.js';
import { STORAGE_CONFIG } from '../config/index.js';

/**
 * 数据存储服务类
 */
export class StorageService {
    constructor(storageType = STORAGE_TYPES.AUTO, options = {}) {
        this.adapter = null;
        this.storageType = storageType;
        this.options = options;
        this.logTime = getCurrentEast8Time();
        this.isInitialized = false;
    }

    /**
     * 初始化存储适配器
     */
    async init() {
        if (!this.isInitialized) {
            try {
                this.adapter = StorageFactory.createAdapter(this.storageType, this.options);
                const success = await this.adapter.init();

                if (success) {
                    console.log(`${this.adapter.getType()}存储初始化成功`);
                    this.isInitialized = true;
                } else {
                    // 如果指定的存储类型失败，尝试降级到内存存储
                    if (this.storageType !== STORAGE_TYPES.MEMORY) {
                        console.warn(`${this.adapter.getType()}存储初始化失败，降级到内存存储`);
                        await this.adapter.cleanup();
                        this.adapter = StorageFactory.createAdapter(STORAGE_TYPES.MEMORY, this.options);
                        const fallbackSuccess = await this.adapter.init();
                        if (fallbackSuccess) {
                            console.log(`已切换到${this.adapter.getType()}存储`);
                            this.isInitialized = true;
                        } else {
                            throw new Error('所有存储适配器初始化失败');
                        }
                    } else {
                        throw new Error('内存存储初始化失败');
                    }
                }
            } catch (error) {
                console.error('存储服务初始化失败:', error);
                throw error;
            }
        }
        return this.adapter;
    }

    /**
     * 获取当前使用的存储类型
     * @returns {string} 存储类型
     */
    getStorageType() {
        return this.adapter ? this.adapter.getType() : 'Unknown';
    }

    /**
     * 获取数据
     * @param {Array} key - 键数组
     * @returns {Promise<any>} 数据值
     */
    async get(key) {
        await this.init();
        return await this.adapter.get(key);
    }

    /**
     * 设置数据
     * @param {Array} key - 键数组
     * @param {any} value - 值
     */
    async set(key, value) {
        await this.init();
        return await this.adapter.set(key, value);
    }

    /**
     * 删除数据
     * @param {Array} key - 键数组
     */
    async delete(key) {
        await this.init();
        return await this.adapter.delete(key);
    }

    /**
     * 清理存储资源
     */
    async cleanup() {
        if (this.adapter) {
            await this.adapter.cleanup();
            this.adapter = null;
        }
        this.isInitialized = false;
    }

    /**
     * 对比数据
     * @param {string} platform - 平台名称
     * @param {object} data - 新数据
     * @returns {Promise<number>} 818表示有更新，520表示无更新，undefined表示初始化
     */
    async compareData(platform, data) {
        console.log(`${this.logTime} : 对比数据:${platform}`);
        
        const oldData = await this.get([platform]);
        
        if (!oldData.value || oldData.value === null) {
            console.log(`${this.logTime} : 数据不存在，初始化数据`);
            await this.set([platform], data);
            return;
        } else if (data[platform] != oldData.value[platform]) {
            // 更新数据
            console.log(`${this.logTime} : 对比数据结果：有更新`);
            await this.set([platform, "old"], oldData.value);
            await this.set([platform], data);
            return 818;
        } else {
            console.log(`${this.logTime} : 对比数据结果：无更新`);
            return 520;
        }
    }

    /**
     * 检查直播状态并处理通知
     * @param {string} platform - 平台名称
     * @param {object} data - 数据
     * @param {number} liveStatus - 直播状态（1或2表示直播中）
     * @returns {Promise<boolean>} 是否需要发送通知
     */
    async checkLiveStatus(platform, data, liveStatus) {
        const oldData = await this.get([platform, "is_notified"]);
        
        if (data[platform] == liveStatus && !oldData.value) {
            // 更新数据
            await this.set([platform, "is_notified"], "true");
            console.log(`${this.logTime}：阿细在${platform}直播啦，发送消息`);
            return true;
        } else if (data[platform] != liveStatus) {
            await this.set([platform, "is_notified"], "false");
        }
        
        return false;
    }

    /**
     * 获取所有平台数据
     * @param {Array<string>} platforms - 平台列表
     * @returns {Promise<Array>} 所有平台数据
     */
    async getAllPlatformData(platforms) {
        const results = [];
        
        for (const platform of platforms) {
            const data = await this.get([platform]);
            results.push({
                platform,
                data: data.value,
            });
        }
        
        return results;
    }

    /**
     * 删除所有平台数据
     * @param {Array<string>} platforms - 平台列表
     */
    async deleteAllPlatformData(platforms) {
        for (const platform of platforms) {
            await this.delete([platform]);
            console.log(`${platform}数据已删除`);
        }
    }
}

// 创建单例实例
export const storageService = new StorageService(STORAGE_CONFIG.type, {
    namespace: STORAGE_CONFIG.cloudflare.namespace
});
