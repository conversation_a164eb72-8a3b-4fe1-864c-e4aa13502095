/**
 * 内存存储适配器
 * 用于本地开发和测试
 */
import { BaseStorageAdapter } from './base.js';

export class MemoryAdapter extends BaseStorageAdapter {
    constructor() {
        super();
        this.storage = new Map();
        this.isInitialized = false;
    }

    /**
     * 初始化内存存储
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async init() {
        this.storage.clear();
        this.isInitialized = true;
        console.log('内存存储初始化成功');
        return true;
    }

    /**
     * 检查存储是否可用
     * @returns {Promise<boolean>} 是否可用
     */
    async isAvailable() {
        if (!this.isInitialized) {
            return await this.init();
        }
        return true; // 内存存储总是可用的
    }

    /**
     * 将键数组转换为字符串键
     * @param {Array|string} key - 键
     * @returns {string} 字符串键
     */
    normalizeKey(key) {
        return Array.isArray(key) ? key.join(':') : key;
    }

    /**
     * 获取数据
     * @param {Array|string} key - 键
     * @returns {Promise<{value: any}>} 数据值
     */
    async get(key) {
        if (!await this.isAvailable()) {
            throw new Error('内存存储不可用');
        }

        const normalizedKey = this.normalizeKey(key);
        const value = this.storage.get(normalizedKey);
        return { value: value || null };
    }

    /**
     * 设置数据
     * @param {Array|string} key - 键
     * @param {any} value - 值
     * @returns {Promise<boolean>} 设置是否成功
     */
    async set(key, value) {
        if (!await this.isAvailable()) {
            throw new Error('内存存储不可用');
        }

        const normalizedKey = this.normalizeKey(key);
        this.storage.set(normalizedKey, value);
        return true;
    }

    /**
     * 删除数据
     * @param {Array|string} key - 键
     * @returns {Promise<boolean>} 删除是否成功
     */
    async delete(key) {
        if (!await this.isAvailable()) {
            throw new Error('内存存储不可用');
        }

        const normalizedKey = this.normalizeKey(key);
        return this.storage.delete(normalizedKey);
    }

    /**
     * 获取存储类型名称
     * @returns {string} 存储类型
     */
    getType() {
        return 'Memory';
    }

    /**
     * 清理资源
     * @returns {Promise<void>}
     */
    async cleanup() {
        this.storage.clear();
        this.isInitialized = false;
    }

    /**
     * 获取存储统计信息
     * @returns {object} 统计信息
     */
    getStats() {
        return {
            size: this.storage.size,
            keys: Array.from(this.storage.keys())
        };
    }
}
