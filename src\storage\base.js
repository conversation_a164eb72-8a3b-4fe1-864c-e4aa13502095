/**
 * 存储适配器基类
 * 定义所有存储后端必须实现的接口
 */
export class BaseStorageAdapter {
    /**
     * 初始化存储
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async init() {
        throw new Error('子类必须实现 init 方法');
    }

    /**
     * 获取数据
     * @param {Array|string} key - 键
     * @returns {Promise<{value: any}>} 数据值
     */
    async get(key) {
        throw new Error('子类必须实现 get 方法');
    }

    /**
     * 设置数据
     * @param {Array|string} key - 键
     * @param {any} value - 值
     * @returns {Promise<boolean>} 设置是否成功
     */
    async set(key, value) {
        throw new Error('子类必须实现 set 方法');
    }

    /**
     * 删除数据
     * @param {Array|string} key - 键
     * @returns {Promise<boolean>} 删除是否成功
     */
    async delete(key) {
        throw new Error('子类必须实现 delete 方法');
    }

    /**
     * 检查存储是否可用
     * @returns {Promise<boolean>} 是否可用
     */
    async isAvailable() {
        throw new Error('子类必须实现 isAvailable 方法');
    }

    /**
     * 获取存储类型名称
     * @returns {string} 存储类型
     */
    getType() {
        throw new Error('子类必须实现 getType 方法');
    }

    /**
     * 清理资源
     * @returns {Promise<void>}
     */
    async cleanup() {
        // 默认实现，子类可以重写
    }
}
