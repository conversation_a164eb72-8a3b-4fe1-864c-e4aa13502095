/**
 * YouTube平台检查器
 */

import { parse } from "jsr:@libs/xml";
import { BasePlatformChecker } from './base.js';
import { PLATFORM_CONFIG } from '../config/index.js';

/**
 * YouTube检查器类
 */
export class YoutubeChecker extends BasePlatformChecker {
    constructor() {
        super("youtube");
        this.config = PLATFORM_CONFIG.youtube;
    }

    /**
     * 初始化检查器
     */
    async init() {
        const data = await this.fetchData();
        const hasUpdate = await this.handleDataUpdate(data);
        
        if (hasUpdate) {
            const message = this.createMarkdownMessage(this.name);
            await this.sendQywx(message);
        }
    }

    /**
     * 获取YouTube数据
     * @returns {Promise<object>} YouTube数据
     */
    async fetchData() {
        try {
            const response = await fetch(this.config.api);
            let result = await response.text();
            
            if (this.isHtmlResponse(result)) {
                console.log(`${this.name}返回数据为html，估计被限制了`);
                return {};
            }
            
            result = parse(result).rss.channel;
            const data = {
                [this.name]: result.item[0].pubDate,
            };
            
            this.logSuccessfulFetch(data);
            return data;
        } catch (error) {
            await this.handleApiError(error, 'Youtube API');
            return {};
        }
    }
}
