/**
 * 微博平台检查器
 */

import { BasePlatformChecker } from './base.js';
import { PLATFORM_CONFIG } from '../config/index.js';

/**
 * 微博检查器类
 */
export class WeiboChecker extends BasePlatformChecker {
    constructor() {
        super("weibo");
        this.config = PLATFORM_CONFIG.weibo;
    }

    /**
     * 初始化检查器
     */
    async init() {
        const data = await this.fetchData();
        const hasUpdate = await this.handleDataUpdate(data);
        
        if (hasUpdate) {
            const message = this.createMarkdownMessage(this.name);
            await this.sendQywx(message);
        }
    }

    /**
     * 获取微博数据
     * @returns {Promise<object>} 微博数据
     */
    async fetchData() {
        try {
            const response = await fetch(this.config.api, {
                "headers": {
                    "accept": "application/json, text/plain, */*",
                    "mweibo-pwa": "1",
                    "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Microsoft Edge\";v=\"133\", \"Chromium\";v=\"133\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "x-requested-with": "XMLHttpRequest",
                    "x-xsrf-token": "b489df",
                    "Referer": "https://m.weibo.cn/u/1770529042",
                    "Referrer-Policy": "strict-origin-when-cross-origin"
                },
                "body": null,
                "method": "GET"
            });
            
            const result = await response.json();
            
            if (result.ok !== 1) {
                console.log(`${this.logTime}：${this.name}通过api获取数据失败，估计被限制了`);
                await this.sendQywxTest(`${this.logTime}：${this.name}通过api获取数据失败，估计被限制了`);
                return {};
            }
            
            const cards = result.data.cards;
            const data = {
                [this.name]: cards[1].mblog.created_at,
            };
            
            this.logSuccessfulFetch(data);
            return data;
        } catch (error) {
            await this.handleApiError(error, '微博API');
            return {};
        }
    }
}
