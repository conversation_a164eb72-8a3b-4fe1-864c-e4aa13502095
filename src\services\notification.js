/**
 * 通知服务模块
 */

import { NOTIFICATION_CONFIG, TEST_MODE } from '../config/index.js';
import { getCurrentEast8Time } from '../utils/time.js';

/**
 * 通知服务类
 */
export class NotificationService {
    constructor() {
        this.logTime = getCurrentEast8Time();
    }

    /**
     * 息知通知
     * @param {object} message - 消息对象
     */
    async xiZhi(message) {
        try {
            const response = await fetch(NOTIFICATION_CONFIG.xiZhi.url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(message)
            });
            
            const data = await response.json();
            console.log(`息知推送成功:${JSON.stringify(data)}`);
        } catch (error) {
            console.error(`息知推送失败：${JSON.stringify(error)}`);
        }
    }

    /**
     * 企业微信机器人
     * @param {object} message - 消息对象
     */
    async sendQywx(message) {
        try {
            const response = await fetch(NOTIFICATION_CONFIG.qywx.url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(message)
            });
            
            const data = await response.json();
            console.log(`${this.logTime} : 企业微信机器人发送成功:${JSON.stringify(data)}`);
        } catch (error) {
            console.error(`${this.logTime} : 企业微信机器人发送失败：${JSON.stringify(error)}`);
        }
    }



    /**
     * wxpusher 推送
     * @param {string} platform - 平台名称
     */
    async sendWxpusher(platform) {
        if (TEST_MODE) {
            return;
        }
        
        try {
            const response = await fetch(NOTIFICATION_CONFIG.wxpusher.url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    appToken: NOTIFICATION_CONFIG.wxpusher.appToken,
                    content: `不提供预览，请自行前往 ${platform} 查看`,
                    summary: `阿细的 ${platform} 有新动态`,
                    contentType: 1,
                    topicIds: NOTIFICATION_CONFIG.wxpusher.topicIds,
                    url: "",
                    verifyPayType: 0
                })
            });
            
            const data = await response.json();
            console.log(`${this.logTime} : wxpusher 推送成功:${JSON.stringify(data)}`);
        } catch (error) {
            console.error(`${this.logTime} : wxpusher 推送失败：${JSON.stringify(error)}`);
        }
    }

    /**
     * 创建新闻类型消息
     * @param {string} platform - 平台名称
     * @param {string} description - 描述
     * @param {string} url - 链接
     * @param {string} picurl - 图片链接
     * @returns {object} 消息对象
     */
    createNewsMessage(platform, description, url, picurl) {
        return {
            "msgtype": "news",
            "news": {
                "articles": [
                    {
                        "title": platform,
                        "description": description,
                        "url": url,
                        "picurl": picurl
                    }
                ]
            }
        };
    }

    /**
     * 创建Markdown类型消息
     * @param {string} platform - 平台名称
     * @param {string} content - 内容
     * @returns {object} 消息对象
     */
    createMarkdownMessage(platform, content) {
        return {
            "msgtype": "markdown",
            "markdown": {
                "content": content || `阿细的 **${platform}** 有新动态`
            }
        };
    }
}
