/**
 * 重构后系统的简单测试
 */

import { appService } from './src/services/app.js';
import { httpServer } from './src/services/server.js';
import { storageService } from './src/services/storage.js';
import { PLATFORMS, CHINESE_MAP } from './src/config/index.js';

console.log('开始测试重构后的系统...\n');

// 测试配置
console.log('1. 测试配置模块:');
console.log(`支持的平台: ${PLATFORMS.join(', ')}`);
console.log(`平台中文映射: ${JSON.stringify(CHINESE_MAP, null, 2)}\n`);

// 测试存储服务
console.log('2. 测试存储服务:');
console.log(`当前存储类型: ${storageService.getStorageType()}`);
await storageService.set(['test'], { value: 'test_data' });
const testData = await storageService.get(['test']);
console.log(`存储测试结果: ${JSON.stringify(testData)}`);
await storageService.delete(['test']);
console.log('存储服务测试通过\n');

// 测试应用服务
console.log('3. 测试应用服务:');
try {
    const report = await appService.getDataReport();
    console.log(`数据报告获取成功，包含 ${report.length} 个平台`);
    console.log('应用服务测试通过\n');
} catch (error) {
    console.error('应用服务测试失败:', error);
}

// 测试HTTP服务器（不启动，只测试路由处理）
console.log('4. 测试HTTP服务器路由:');
const testRoutes = ['/health', '/data', '/xiaohongshu', '/delete'];
console.log('HTTP服务器路由测试跳过（需要实际启动服务器）');

console.log('\n测试完成！重构后的系统模块化架构正常工作。');
