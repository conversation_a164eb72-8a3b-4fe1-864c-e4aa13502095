/**
 * 平台检查器基类
 */

import { NotificationService } from '../services/notification.js';
import { storageService } from '../services/storage.js';
import { getCurrentEast8Time } from '../utils/time.js';

/**
 * 平台检查器基类
 */
export class BasePlatformChecker extends NotificationService {
    constructor(platformName) {
        super();
        this.name = platformName;
        this.logTime = getCurrentEast8Time();
        this.storageService = storageService;
    }

    /**
     * 初始化检查器
     * 子类需要实现此方法
     */
    async init() {
        throw new Error('子类必须实现 init 方法');
    }

    /**
     * 获取平台数据
     * 子类需要实现此方法
     * @returns {Promise<object>} 平台数据
     */
    async fetchData() {
        throw new Error('子类必须实现 fetchData 方法');
    }

    /**
     * 处理数据更新
     * @param {object} data - 平台数据
     * @returns {Promise<boolean>} 是否有更新
     */
    async handleDataUpdate(data) {
        if (Object.keys(data).length === 0) {
            console.log(`${this.logTime} : 获取${this.name}数据失败`);
            const message = {
                "msgtype": "text",
                "text": {
                    "content": `${this.logTime} : 获取${this.name}数据失败`
                }
            };
            await this.sendQywx(message);
            return false;
        }

        const changes = await this.storageService.compareData(this.name, data);
        
        if (changes === 818) {
            await this.sendWxpusher(this.name);
            return true;
        }
        
        return false;
    }

    /**
     * 处理直播状态更新
     * @param {object} data - 平台数据
     * @param {number} liveStatus - 直播状态
     * @returns {Promise<boolean>} 是否需要发送通知
     */
    async handleLiveStatusUpdate(data, liveStatus) {
        if (Object.keys(data).length === 0) {
            console.log(`${this.logTime}：获取${this.name}数据失败`);
            const message = {
                "msgtype": "text",
                "text": {
                    "content": `${this.logTime}：获取${this.name}数据失败`
                }
            };
            await this.sendQywx(message);
            return false;
        }

        return await this.storageService.checkLiveStatus(this.name, data, liveStatus);
    }

    /**
     * 处理API错误
     * @param {Error} error - 错误对象
     * @param {string} apiName - API名称
     */
    async handleApiError(error, apiName = 'API') {
        const errorMessage = `${this.logTime} : 从${apiName}获取${this.name}数据失败：${error}`;
        console.error(errorMessage);
        const message = {
            "msgtype": "text",
            "text": {
                "content": errorMessage
            }
        };
        await this.sendQywx(message);
    }

    /**
     * 检查响应是否为HTML（通常表示被限制）
     * @param {string} responseText - 响应文本
     * @returns {boolean} 是否为HTML
     */
    isHtmlResponse(responseText) {
        return responseText.trim().startsWith('<html');
    }

    /**
     * 记录成功获取数据
     * @param {object} data - 数据
     */
    logSuccessfulFetch(data) {
        console.log(`获取${this.name}数据成功:${JSON.stringify(data)}`);
    }

    /**
     * 运行检查器
     * 通用的运行流程，子类可以重写
     */
    async run() {
        try {
            await this.init();
            console.log(`实例化${this.name}完成`);
        } catch (error) {
            console.error(`实例化${this.name}失败`, error);
            const message = {
                "msgtype": "text",
                "text": {
                    "content": `实例化${this.name}失败，请检查日志`
                }
            };
            await this.sendQywx(message);
            throw error;
        }
    }
}
