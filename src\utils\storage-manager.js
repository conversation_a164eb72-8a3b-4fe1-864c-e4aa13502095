/**
 * 存储管理工具
 * 提供存储类型检测、切换和管理功能
 */
import { StorageFactory, STORAGE_TYPES } from '../storage/factory.js';

/**
 * 存储管理器
 */
export class StorageManager {
    /**
     * 检测当前环境支持的存储类型
     * @returns {Promise<object>} 检测结果
     */
    static async detectEnvironment() {
        console.log('🔍 检测存储环境...\n');
        
        const results = {
            environment: 'unknown',
            supportedTypes: [],
            recommended: null,
            details: {}
        };

        // 检测运行环境
        if (typeof Deno !== 'undefined') {
            results.environment = 'Deno';
        } else if (typeof globalThis.MY_KV_NAMESPACE !== 'undefined') {
            results.environment = 'Cloudflare Workers';
        } else if (typeof window !== 'undefined') {
            results.environment = 'Browser';
        } else if (typeof global !== 'undefined') {
            results.environment = 'Node.js';
        }

        console.log(`📍 运行环境: ${results.environment}`);

        // 检测Deno KV
        try {
            const denoAdapter = StorageFactory.createAdapter(STORAGE_TYPES.DENO_KV);
            const available = await denoAdapter.isAvailable();
            results.details.denoKV = {
                available,
                reason: available ? '可用' : 'Deno.openKv 不可用或运行环境不支持'
            };
            if (available) {
                results.supportedTypes.push(STORAGE_TYPES.DENO_KV);
            }
            await denoAdapter.cleanup();
        } catch (error) {
            results.details.denoKV = {
                available: false,
                reason: error.message
            };
        }

        // 检测Cloudflare KV
        try {
            const cfAdapter = StorageFactory.createAdapter(STORAGE_TYPES.CLOUDFLARE_KV);
            const available = await cfAdapter.isAvailable();
            results.details.cloudflareKV = {
                available,
                reason: available ? '可用' : 'KV namespace 未找到或不可用'
            };
            if (available) {
                results.supportedTypes.push(STORAGE_TYPES.CLOUDFLARE_KV);
            }
            await cfAdapter.cleanup();
        } catch (error) {
            results.details.cloudflareKV = {
                available: false,
                reason: error.message
            };
        }

        // 内存存储总是可用
        results.supportedTypes.push(STORAGE_TYPES.MEMORY);
        results.details.memory = {
            available: true,
            reason: '总是可用（仅用于开发和测试）'
        };

        // 确定推荐类型
        results.recommended = await StorageFactory.getRecommendedType();

        return results;
    }

    /**
     * 打印存储环境报告
     * @returns {Promise<void>}
     */
    static async printEnvironmentReport() {
        const results = await StorageManager.detectEnvironment();
        
        console.log('\n📊 存储类型支持情况:');
        console.log('─'.repeat(50));
        
        // Deno KV
        const denoStatus = results.details.denoKV.available ? '✅' : '❌';
        console.log(`${denoStatus} Deno KV: ${results.details.denoKV.reason}`);
        
        // Cloudflare KV
        const cfStatus = results.details.cloudflareKV.available ? '✅' : '❌';
        console.log(`${cfStatus} Cloudflare KV: ${results.details.cloudflareKV.reason}`);
        
        // Memory
        const memoryStatus = results.details.memory.available ? '✅' : '❌';
        console.log(`${memoryStatus} Memory: ${results.details.memory.reason}`);
        
        console.log('\n🎯 推荐使用:', results.recommended);
        console.log('📋 支持的类型:', results.supportedTypes.join(', '));
        
        return results;
    }

    /**
     * 测试存储适配器
     * @param {string} type - 存储类型
     * @param {object} options - 配置选项
     * @returns {Promise<object>} 测试结果
     */
    static async testAdapter(type, options = {}) {
        console.log(`\n🧪 测试 ${type} 存储适配器...`);
        
        const result = {
            type,
            success: false,
            error: null,
            operations: {
                init: false,
                set: false,
                get: false,
                delete: false
            }
        };

        try {
            // 创建适配器
            const adapter = StorageFactory.createAdapter(type, options);
            
            // 测试初始化
            result.operations.init = await adapter.init();
            if (!result.operations.init) {
                throw new Error('初始化失败');
            }
            
            // 测试设置数据
            const testKey = ['test', 'storage-manager', Date.now().toString()];
            const testValue = { message: 'Hello Storage!', timestamp: new Date().toISOString() };
            result.operations.set = await adapter.set(testKey, testValue);
            
            // 测试获取数据
            const retrieved = await adapter.get(testKey);
            result.operations.get = retrieved.value !== null && 
                                   retrieved.value.message === testValue.message;
            
            // 测试删除数据
            result.operations.delete = await adapter.delete(testKey);
            
            // 清理
            await adapter.cleanup();
            
            result.success = Object.values(result.operations).every(op => op === true);
            
        } catch (error) {
            result.error = error.message;
        }

        // 打印结果
        const status = result.success ? '✅ 通过' : '❌ 失败';
        console.log(`${status} ${type} 存储测试`);
        
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        } else {
            console.log(`   初始化: ${result.operations.init ? '✅' : '❌'}`);
            console.log(`   写入: ${result.operations.set ? '✅' : '❌'}`);
            console.log(`   读取: ${result.operations.get ? '✅' : '❌'}`);
            console.log(`   删除: ${result.operations.delete ? '✅' : '❌'}`);
        }

        return result;
    }

    /**
     * 运行完整的存储测试套件
     * @returns {Promise<object>} 测试结果汇总
     */
    static async runTestSuite() {
        console.log('🚀 开始存储适配器测试套件\n');
        
        const environment = await StorageManager.printEnvironmentReport();
        const testResults = [];

        // 测试所有支持的存储类型
        for (const type of environment.supportedTypes) {
            const result = await StorageManager.testAdapter(type);
            testResults.push(result);
        }

        // 汇总结果
        const summary = {
            environment,
            tests: testResults,
            passed: testResults.filter(r => r.success).length,
            failed: testResults.filter(r => !r.success).length,
            total: testResults.length
        };

        console.log('\n📈 测试汇总:');
        console.log('─'.repeat(30));
        console.log(`总计: ${summary.total}`);
        console.log(`通过: ${summary.passed}`);
        console.log(`失败: ${summary.failed}`);
        console.log(`成功率: ${((summary.passed / summary.total) * 100).toFixed(1)}%`);

        return summary;
    }
}
