/**
 * 今日头条平台检查器
 */

import { BasePlatformChecker } from './base.js';
import { PLATFORM_CONFIG } from '../config/index.js';
import { formatTimestamp } from '../utils/time.js';

/**
 * 今日头条检查器类
 */
export class <PERSON>tt<PERSON>he<PERSON> extends BasePlatformChecker {
    constructor() {
        super("jrtt");
        this.config = PLATFORM_CONFIG.jrtt;
    }

    /**
     * 初始化检查器
     */
    async init() {
        const data = await this.fetchData();
        const hasUpdate = await this.handleDataUpdate(data);
        
        if (hasUpdate) {
            const message = this.createMarkdownMessage(this.name, `阿细 **今日头条** 有新动态`);
            await this.sendQywx(message);
        }
    }

    /**
     * 获取今日头条数据
     * @returns {Promise<object>} 今日头条数据
     */
    async fetchData() {
        try {
            console.log("通过接口获取今日头条数据");
            
            const apiUrl = this.config.api + "?category=profile_all&token=MS4wLjABAAAAFJ-UpRm-b-wkM_qWY8AMxB6ZbeXRSCfC1CBI6MBpx10&max_behot_time=0&entrance_gid=&aid=24&app_name=toutiao_web&a_bogus=OX80QfLkdDViDDWV54nLfY3qVXN3Y2EV0t9bMDhqexVP1y39HMYF9exoNrTvwcEjx4%2FhIeYjy4hbYrnhrQ270Hwf9WXE%2F25gmDSkKl5Q5xSSs1X9CLXgJ00Nmkt5tFn2RvMWrOX%2Fow-HFb82ldAJ5kIlO62-zo0%2F9-j%3D";
            
            const response = await fetch(apiUrl, {
                "headers": {
                    "accept": "application/json, text/plain, */*",
                    "accept-language": "zh-CN,zh;q=0.9",
                    "cache-control": "no-cache",
                    "pragma": "no-cache",
                    "priority": "u=1, i",
                    "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Microsoft Edge\";v=\"133\", \"Chromium\";v=\"133\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "sec-gpc": "1",
                    "cookie": "__ac_nonce=067b452570058a402d798; __ac_signature=_02B4Z6wo00f01RxNETwAAIDD-UQozvTriJkcbRWAACCp8b; tt_webid=7472688133131683340; gfkadpd=24,6457; ttcid=396a59483eb242b6824c534ea451f70020; ttwid=1%7CXPQoFlzKPks9ISE1Wzj_Sl4hHbdYEbkp6eslnlFftIQ%7C1739870809%7C218aac0ad0a3174df456eb91f17900c9b3fada0b188c6fa0eaaf2789c9569660; local_city_cache=%E4%BD%9B%E5%B1%B1; csrftoken=bd2d5e93431d885bfcc350b6465e72ae",
                    "Referer": "https://www.toutiao.com/c/user/token/MS4wLjABAAAAFJ-UpRm-b-wkM_qWY8AMxB6ZbeXRSCfC1CBI6MBpx10/?wid=1739870806885",
                    "Referrer-Policy": "strict-origin-when-cross-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0"
                },
                "body": null,
                "method": "GET"
            });
            
            const result = await response.json();
            
            // 解析数据
            if (result.message !== 'success') {
                console.log(`${this.logTime}：${this.name}接口返回数据异常`);
                const message = {
                    "msgtype": "text",
                    "text": {
                        "content": `${this.logTime}：${this.name}接口返回数据异常`
                    }
                };
                await this.sendQywx(message);
                return {};
            }
            
            const data = {
                [this.name]: formatTimestamp(result.data[0].create_time)
            };
            
            console.log(`今日头条数据：${JSON.stringify(data)}`);
            return data;
        } catch (error) {
            console.log(`${this.name}接口获取数据失败`);
            const message = {
                "msgtype": "text",
                "text": {
                    "content": `${this.name}接口获取数据失败`
                }
            };
            await this.sendQywx(message);
            return {};
        }
    }
}
