# 更新日志

## [2.2.0] - 2025-07-02

### 新增
- 🏗️ **模块化存储架构** - 重构存储系统，支持多种存储后端
- 📦 **存储适配器** - 支持 Deno KV、Cloudflare KV、内存存储
- 🔧 **存储工厂模式** - 统一的存储适配器创建和管理
- 🎯 **自动存储检测** - 智能检测环境并选择最佳存储类型
- 📊 **存储管理工具** - 提供存储环境检测和测试功能
- ⚙️ **环境变量配置** - 通过环境变量灵活配置存储类型
- 🧪 **存储测试套件** - 完整的存储功能测试和性能测试

### 重构
- 🔄 **存储服务重构** - 使用适配器模式替代硬编码的存储逻辑
- 📝 **配置系统增强** - 添加存储配置选项和环境变量支持
- 🛠️ **错误处理改进** - 更好的存储初始化失败处理和自动降级

### 新增文件
- `src/storage/base.js` - 存储适配器基类
- `src/storage/deno-kv.js` - Deno KV 存储适配器
- `src/storage/cloudflare-kv.js` - Cloudflare KV 存储适配器
- `src/storage/memory.js` - 内存存储适配器
- `src/storage/factory.js` - 存储工厂类
- `src/utils/storage-manager.js` - 存储管理工具
- `test-storage.js` - 存储测试套件
- `storage-config-examples.md` - 存储配置示例文档

### 优化
- 🚀 **部署灵活性** - 支持 Deno Deploy、Cloudflare Workers 等多种部署环境
- 📈 **可扩展性** - 易于添加新的存储后端支持
- 🔍 **可观测性** - 提供存储类型检测和性能监控
- 📚 **文档完善** - 详细的配置说明和使用示例

## [2.1.0] - 2025-07-02

### 移除
- 删除了 `sendQywxTest` 企业微信机器人测试方法
- 移除了 `qywxTest` 配置项

### 修改
- 所有原本使用 `sendQywxTest` 的地方现在使用 `sendQywx` 方法
- 统一使用企业微信机器人进行错误通知和状态报告
- 消息格式统一为标准的企业微信文本消息格式

### 影响的文件
- `src/services/notification.js` - 删除 `sendQywxTest` 方法
- `src/config/index.js` - 删除 `qywxTest` 配置
- `src/services/app.js` - 替换所有 `sendQywxTest` 调用
- `src/checkers/base.js` - 替换所有 `sendQywxTest` 调用
- `src/checkers/douyin.js` - 替换 `sendQywxTest` 调用
- `src/checkers/jrtt.js` - 替换 `sendQywxTest` 调用
- `src/checkers/weibo.js` - 替换 `sendQywxTest` 调用

### 技术细节
- 所有错误消息现在使用统一的消息格式：
  ```javascript
  {
    "msgtype": "text",
    "text": {
      "content": "错误消息内容"
    }
  }
  ```

## [2.0.0] - 2025-07-02

### 新增
- 模块化架构重构
- Deno KV存储支持（自动降级到内存存储）
- 智能存储策略
- 统一的错误处理
- HTTP API接口
- 配置集中管理

### 重构
- 将单文件 874 行代码重构为模块化架构
- 创建基于继承的平台检查器系统
- 实现服务层分离（存储、通知、应用、HTTP）
- 添加工具模块（时间处理）

### 优化
- 代码可维护性大幅提升
- 易于扩展新平台
- 统一的日志记录
- 更好的错误处理
