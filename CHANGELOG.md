# 更新日志

## [2.1.0] - 2025-07-02

### 移除
- 删除了 `sendQywxTest` 企业微信机器人测试方法
- 移除了 `qywxTest` 配置项

### 修改
- 所有原本使用 `sendQywxTest` 的地方现在使用 `sendQywx` 方法
- 统一使用企业微信机器人进行错误通知和状态报告
- 消息格式统一为标准的企业微信文本消息格式

### 影响的文件
- `src/services/notification.js` - 删除 `sendQywxTest` 方法
- `src/config/index.js` - 删除 `qywxTest` 配置
- `src/services/app.js` - 替换所有 `sendQywxTest` 调用
- `src/checkers/base.js` - 替换所有 `sendQywxTest` 调用
- `src/checkers/douyin.js` - 替换 `sendQywxTest` 调用
- `src/checkers/jrtt.js` - 替换 `sendQywxTest` 调用
- `src/checkers/weibo.js` - 替换 `sendQywxTest` 调用

### 技术细节
- 所有错误消息现在使用统一的消息格式：
  ```javascript
  {
    "msgtype": "text",
    "text": {
      "content": "错误消息内容"
    }
  }
  ```

## [2.0.0] - 2025-07-02

### 新增
- 模块化架构重构
- Deno KV存储支持（自动降级到内存存储）
- 智能存储策略
- 统一的错误处理
- HTTP API接口
- 配置集中管理

### 重构
- 将单文件 874 行代码重构为模块化架构
- 创建基于继承的平台检查器系统
- 实现服务层分离（存储、通知、应用、HTTP）
- 添加工具模块（时间处理）

### 优化
- 代码可维护性大幅提升
- 易于扩展新平台
- 统一的日志记录
- 更好的错误处理
