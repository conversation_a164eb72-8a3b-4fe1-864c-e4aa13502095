/**
 * 哔哩哔哩平台检查器
 */

import { parse } from "jsr:@libs/xml";
import { BasePlatformChecker } from './base.js';
import { PLATFORM_CONFIG } from '../config/index.js';

/**
 * 哔哩哔哩检查器类
 */
export class BilibiliChecker extends BasePlatformChecker {
    constructor() {
        super("哔哩哔哩");
        this.config = PLATFORM_CONFIG["哔哩哔哩"];
    }

    /**
     * 初始化检查器
     */
    async init() {
        const data = await this.fetchData();
        const hasUpdate = await this.handleDataUpdate(data);
        
        if (hasUpdate) {
            const message = this.createMarkdownMessage(this.name);
            await this.sendQywx(message);
        }
    }

    /**
     * 获取哔哩哔哩数据
     * @returns {Promise<object>} 哔哩哔哩数据
     */
    async fetchData() {
        try {
            const response = await fetch(this.config.api);
            let result = await response.text();
            
            if (this.isHtmlResponse(result)) {
                console.log(`${this.name}返回数据为html，估计被限制了`);
                return {};
            }
            
            result = parse(result).rss.channel;
            const data = {
                [this.name]: result.item[0].pubDate
            };
            
            this.logSuccessfulFetch(data);
            return data;
        } catch (error) {
            await this.handleApiError(error, 'Bilibili API');
            return {};
        }
    }
}

/**
 * 哔哩哔哩直播检查器类
 */
export class BilibiliLiveChecker extends BasePlatformChecker {
    constructor() {
        super("哔哩哔哩直播");
        this.config = PLATFORM_CONFIG["哔哩哔哩直播"];
    }

    /**
     * 初始化检查器
     */
    async init() {
        const data = await this.fetchData();
        const shouldNotify = await this.handleLiveStatusUpdate(data, 1);
        
        if (shouldNotify) {
            const message = this.createNewsMessage(
                this.name,
                `阿细在哔哩哔哩直播啦，快来围观！`,
                this.config.url,
                data["图片链接"]
            );
            await this.sendQywx(message);
        }
    }

    /**
     * 获取哔哩哔哩直播数据
     * @returns {Promise<object>} 哔哩哔哩直播数据
     */
    async fetchData() {
        try {
            const response = await fetch(this.config.api);
            const result = await response.json();
            
            if (result.code !== 0) {
                console.log(`${this.logTime}：${this.name}请求api失败，估计被限制了`);
                return {};
            }
            
            const data = {
                [this.name]: result.data.live_status,
                "图片链接": result.data.user_cover,
            };
            
            return data;
        } catch (error) {
            await this.handleApiError(error, 'Bilibili API');
            return {};
        }
    }
}
