/**
 * Deno KV 存储适配器
 */
import { BaseStorageAdapter } from './base.js';

export class DenoKVAdapter extends BaseStorageAdapter {
    constructor() {
        super();
        this.kv = null;
        this.isInitialized = false;
    }

    /**
     * 初始化Deno KV存储
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async init() {
        try {
            // 检查Deno.openKv是否可用
            if (typeof Deno === 'undefined' || typeof Deno.openKv !== 'function') {
                return false;
            }

            this.kv = await Deno.openKv();
            this.isInitialized = true;
            console.log('Deno KV存储初始化成功');
            return true;
        } catch (error) {
            console.error('Deno KV存储初始化失败:', error);
            this.isInitialized = false;
            return false;
        }
    }

    /**
     * 检查存储是否可用
     * @returns {Promise<boolean>} 是否可用
     */
    async isAvailable() {
        if (!this.isInitialized) {
            return await this.init();
        }
        return this.kv !== null;
    }

    /**
     * 获取数据
     * @param {Array|string} key - 键
     * @returns {Promise<{value: any}>} 数据值
     */
    async get(key) {
        if (!await this.isAvailable()) {
            throw new Error('Deno KV存储不可用');
        }

        const normalizedKey = Array.isArray(key) ? key : [key];
        return await this.kv.get(normalizedKey);
    }

    /**
     * 设置数据
     * @param {Array|string} key - 键
     * @param {any} value - 值
     * @returns {Promise<boolean>} 设置是否成功
     */
    async set(key, value) {
        if (!await this.isAvailable()) {
            throw new Error('Deno KV存储不可用');
        }

        const normalizedKey = Array.isArray(key) ? key : [key];
        const result = await this.kv.set(normalizedKey, value);
        return result.ok;
    }

    /**
     * 删除数据
     * @param {Array|string} key - 键
     * @returns {Promise<boolean>} 删除是否成功
     */
    async delete(key) {
        if (!await this.isAvailable()) {
            throw new Error('Deno KV存储不可用');
        }

        const normalizedKey = Array.isArray(key) ? key : [key];
        await this.kv.delete(normalizedKey);
        return true;
    }

    /**
     * 获取存储类型名称
     * @returns {string} 存储类型
     */
    getType() {
        return 'Deno KV';
    }

    /**
     * 清理资源
     * @returns {Promise<void>}
     */
    async cleanup() {
        if (this.kv && typeof this.kv.close === 'function') {
            await this.kv.close();
        }
        this.kv = null;
        this.isInitialized = false;
    }
}
