/**
 * 存储系统测试文件
 */

import { StorageManager } from './src/utils/storage-manager.js';
import { StorageFactory, STORAGE_TYPES } from './src/storage/factory.js';
import { storageService } from './src/services/storage.js';

console.log('🧪 存储系统测试套件\n');

// 1. 运行环境检测和完整测试套件
console.log('═'.repeat(60));
console.log('📋 第一部分：存储适配器测试套件');
console.log('═'.repeat(60));

const testSummary = await StorageManager.runTestSuite();

// 2. 测试当前配置的存储服务
console.log('\n═'.repeat(60));
console.log('📋 第二部分：当前存储服务测试');
console.log('═'.repeat(60));

console.log('\n🔧 测试当前配置的存储服务...');
try {
    console.log(`📍 当前存储类型: ${storageService.getStorageType()}`);
    
    // 测试基本操作
    const testKey = ['test', 'service', Date.now().toString()];
    const testValue = { 
        message: 'Storage Service Test', 
        timestamp: new Date().toISOString(),
        data: { count: 42, active: true }
    };
    
    console.log('📝 测试写入操作...');
    await storageService.set(testKey, testValue);
    console.log('✅ 写入成功');
    
    console.log('📖 测试读取操作...');
    const retrieved = await storageService.get(testKey);
    console.log('✅ 读取成功');
    console.log(`📄 读取的数据: ${JSON.stringify(retrieved.value)}`);
    
    // 验证数据完整性
    if (retrieved.value && retrieved.value.message === testValue.message) {
        console.log('✅ 数据完整性验证通过');
    } else {
        console.log('❌ 数据完整性验证失败');
    }
    
    console.log('🗑️ 测试删除操作...');
    await storageService.delete(testKey);
    console.log('✅ 删除成功');
    
    // 验证删除
    const deletedCheck = await storageService.get(testKey);
    if (deletedCheck.value === null) {
        console.log('✅ 删除验证通过');
    } else {
        console.log('❌ 删除验证失败');
    }
    
    console.log('\n🎉 存储服务测试全部通过！');
    
} catch (error) {
    console.error('❌ 存储服务测试失败:', error);
}

// 3. 测试存储类型切换
console.log('\n═'.repeat(60));
console.log('📋 第三部分：存储类型切换测试');
console.log('═'.repeat(60));

console.log('\n🔄 测试存储类型切换...');

// 测试不同存储类型的创建
const storageTypes = [STORAGE_TYPES.MEMORY, STORAGE_TYPES.DENO_KV, STORAGE_TYPES.CLOUDFLARE_KV];

for (const type of storageTypes) {
    try {
        console.log(`\n🧪 测试创建 ${type} 存储适配器...`);
        const adapter = StorageFactory.createAdapter(type);
        console.log(`✅ ${type} 适配器创建成功`);
        
        const available = await adapter.isAvailable();
        console.log(`📊 ${type} 可用性: ${available ? '✅ 可用' : '❌ 不可用'}`);
        
        await adapter.cleanup();
    } catch (error) {
        console.log(`❌ ${type} 适配器测试失败: ${error.message}`);
    }
}

// 4. 性能测试
console.log('\n═'.repeat(60));
console.log('📋 第四部分：性能测试');
console.log('═'.repeat(60));

console.log('\n⚡ 运行性能测试...');

try {
    const iterations = 100;
    const testData = { value: 'performance test data', index: 0 };
    
    console.log(`📊 执行 ${iterations} 次读写操作...`);
    
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
        testData.index = i;
        await storageService.set(['perf', i.toString()], testData);
        await storageService.get(['perf', i.toString()]);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    const opsPerSecond = (iterations * 2) / (duration / 1000); // 2 operations per iteration
    
    console.log(`⏱️ 总耗时: ${duration.toFixed(2)}ms`);
    console.log(`🚀 平均性能: ${opsPerSecond.toFixed(2)} 操作/秒`);
    console.log(`📈 单次操作平均耗时: ${(duration / (iterations * 2)).toFixed(2)}ms`);
    
    // 清理性能测试数据
    console.log('🧹 清理性能测试数据...');
    for (let i = 0; i < iterations; i++) {
        await storageService.delete(['perf', i.toString()]);
    }
    console.log('✅ 清理完成');
    
} catch (error) {
    console.error('❌ 性能测试失败:', error);
}

// 5. 总结
console.log('\n═'.repeat(60));
console.log('📋 测试总结');
console.log('═'.repeat(60));

console.log('\n📊 测试结果汇总:');
console.log(`🌍 运行环境: ${testSummary.environment.environment}`);
console.log(`🎯 推荐存储: ${testSummary.environment.recommended}`);
console.log(`📦 支持的存储类型: ${testSummary.environment.supportedTypes.join(', ')}`);
console.log(`🧪 适配器测试: ${testSummary.passed}/${testSummary.total} 通过`);
console.log(`💾 当前使用: ${storageService.getStorageType()}`);

if (testSummary.passed === testSummary.total) {
    console.log('\n🎉 所有存储测试通过！存储系统工作正常。');
} else {
    console.log('\n⚠️ 部分存储测试失败，请检查配置和环境。');
}

console.log('\n✨ 存储系统测试完成！');
