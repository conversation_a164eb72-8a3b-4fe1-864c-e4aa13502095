/**
 * 存储工厂类
 * 负责创建和管理不同类型的存储适配器
 */
import { DenoKVAdapter } from './deno-kv.js';
import { CloudflareKVAdapter } from './cloudflare-kv.js';
import { MemoryAdapter } from './memory.js';

// 存储类型常量
export const STORAGE_TYPES = {
    DENO_KV: 'deno-kv',
    CLOUDFLARE_KV: 'cloudflare-kv',
    MEMORY: 'memory',
    AUTO: 'auto'
};

/**
 * 存储工厂类
 */
export class StorageFactory {
    /**
     * 创建存储适配器
     * @param {string} type - 存储类型
     * @param {object} options - 配置选项
     * @returns {BaseStorageAdapter} 存储适配器实例
     */
    static createAdapter(type = STORAGE_TYPES.AUTO, options = {}) {
        switch (type) {
            case STORAGE_TYPES.DENO_KV:
                return new DenoKVAdapter();
                
            case STORAGE_TYPES.CLOUDFLARE_KV:
                return new CloudflareKVAdapter(options.namespace);
                
            case STORAGE_TYPES.MEMORY:
                return new MemoryAdapter();
                
            case STORAGE_TYPES.AUTO:
                return StorageFactory.createAutoAdapter(options);
                
            default:
                throw new Error(`不支持的存储类型: ${type}`);
        }
    }

    /**
     * 自动选择最佳的存储适配器
     * @param {object} options - 配置选项
     * @returns {BaseStorageAdapter} 存储适配器实例
     */
    static createAutoAdapter(options = {}) {
        // 优先级顺序：Deno KV > Cloudflare KV > Memory
        const adapters = [
            () => new DenoKVAdapter(),
            () => new CloudflareKVAdapter(options.namespace),
            () => new MemoryAdapter()
        ];

        // 返回第一个可用的适配器
        for (const createAdapter of adapters) {
            try {
                const adapter = createAdapter();
                return adapter;
            } catch (error) {
                console.warn(`创建存储适配器失败:`, error);
                continue;
            }
        }

        // 如果所有适配器都失败，返回内存存储作为最后的备选
        return new MemoryAdapter();
    }

    /**
     * 检测当前环境支持的存储类型
     * @returns {Promise<Array<string>>} 支持的存储类型列表
     */
    static async detectSupportedTypes() {
        const supportedTypes = [];

        // 检测Deno KV
        try {
            const denoAdapter = new DenoKVAdapter();
            if (await denoAdapter.isAvailable()) {
                supportedTypes.push(STORAGE_TYPES.DENO_KV);
            }
            await denoAdapter.cleanup();
        } catch (error) {
            // 忽略错误
        }

        // 检测Cloudflare KV
        try {
            const cfAdapter = new CloudflareKVAdapter();
            if (await cfAdapter.isAvailable()) {
                supportedTypes.push(STORAGE_TYPES.CLOUDFLARE_KV);
            }
            await cfAdapter.cleanup();
        } catch (error) {
            // 忽略错误
        }

        // 内存存储总是支持的
        supportedTypes.push(STORAGE_TYPES.MEMORY);

        return supportedTypes;
    }

    /**
     * 获取推荐的存储类型
     * @returns {Promise<string>} 推荐的存储类型
     */
    static async getRecommendedType() {
        const supportedTypes = await StorageFactory.detectSupportedTypes();
        
        // 按优先级返回推荐类型
        if (supportedTypes.includes(STORAGE_TYPES.DENO_KV)) {
            return STORAGE_TYPES.DENO_KV;
        }
        
        if (supportedTypes.includes(STORAGE_TYPES.CLOUDFLARE_KV)) {
            return STORAGE_TYPES.CLOUDFLARE_KV;
        }
        
        return STORAGE_TYPES.MEMORY;
    }
}
