/**
 * HTTP服务器模块
 */

import { appService } from './app.js';
import { getSupportedPlatforms } from '../checkers/index.js';

/**
 * HTTP服务器类
 */
export class HttpServer {
    constructor() {
        this.appService = appService;
        this.setupRoutes();
    }

    /**
     * 设置路由处理器
     */
    setupRoutes() {
        this.pathHandlers = new Map();
        
        // 为每个支持的平台创建路由
        const supportedPlatforms = getSupportedPlatforms();
        supportedPlatforms.forEach(platform => {
            this.pathHandlers.set(`/${platform}`, this.handleManualCheck.bind(this));
        });

        // 系统路由
        this.pathHandlers.set('/data', this.handleDataReport.bind(this));
        this.pathHandlers.set('/delete', this.handleDeleteData.bind(this));
        this.pathHandlers.set('/favicon.ico', this.handleFavicon.bind(this));
        this.pathHandlers.set('/health', this.handleHealthCheck.bind(this));
    }

    /**
     * 处理手动检查请求
     * @param {URL} url - 请求URL
     * @returns {Response} HTTP响应
     */
    async handleManualCheck(url) {
        try {
            const platform = url.pathname.substring(1); // 移除开头的 '/'
            const result = await this.appService.checkSinglePlatform(platform);
            
            return new Response(result, {
                headers: {
                    'Content-Type': 'text/plain; charset=utf-8'
                }
            });
        } catch (error) {
            return new Response(`检查失败: ${error.message}`, {
                status: 500,
                headers: {
                    'Content-Type': 'text/plain; charset=utf-8'
                }
            });
        }
    }

    /**
     * 处理数据报告请求
     * @returns {Response} HTTP响应
     */
    async handleDataReport() {
        try {
            const report = await this.appService.getDataReport();
            
            return new Response(JSON.stringify(report, null, 2), {
                headers: {
                    'Content-Type': 'application/json; charset=utf-8'
                }
            });
        } catch (error) {
            return new Response(`获取数据报告失败: ${error.message}`, {
                status: 500,
                headers: {
                    'Content-Type': 'text/plain; charset=utf-8'
                }
            });
        }
    }

    /**
     * 处理删除数据请求
     * @returns {Response} HTTP响应
     */
    async handleDeleteData() {
        try {
            const result = await this.appService.deleteAllData();
            
            return new Response(result, {
                headers: {
                    'Content-Type': 'text/plain; charset=utf-8'
                }
            });
        } catch (error) {
            return new Response(`删除数据失败: ${error.message}`, {
                status: 500,
                headers: {
                    'Content-Type': 'text/plain; charset=utf-8'
                }
            });
        }
    }

    /**
     * 处理favicon请求
     * @returns {Response} HTTP响应
     */
    handleFavicon() {
        return new Response(null, { status: 204 });
    }

    /**
     * 处理健康检查请求
     * @returns {Response} HTTP响应
     */
    handleHealthCheck() {
        return new Response(JSON.stringify({
            status: 'ok',
            timestamp: new Date().toISOString(),
            service: 'ax-check'
        }), {
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            }
        });
    }

    /**
     * 处理默认请求
     * @returns {Response} HTTP响应
     */
    handleDefault() {
        return new Response("这里没啥好看的", {
            headers: {
                'Content-Type': 'text/plain; charset=utf-8'
            }
        });
    }

    /**
     * 请求处理器
     * @param {Request} req - HTTP请求
     * @returns {Response} HTTP响应
     */
    async handleRequest(req) {
        const url = new URL(req.url);
        console.log(`接收到请求：${url.pathname}`);
        
        const handler = this.pathHandlers.get(url.pathname);
        
        if (handler) {
            return await handler(url);
        } else {
            return this.handleDefault();
        }
    }

    /**
     * 启动HTTP服务器
     * @param {number} port - 端口号
     */
    start(port = 8000) {
        Deno.serve({ port }, (req) => this.handleRequest(req));
        console.log(`HTTP服务器已启动，监听端口 ${port}`);
    }
}

// 创建单例实例
export const httpServer = new HttpServer();
