/**
 * 抖音平台检查器
 */

import { BasePlatformChecker } from './base.js';
import { PLATFORM_CONFIG } from '../config/index.js';
import { formatTimestamp } from '../utils/time.js';

/**
 * 抖音检查器类
 */
export class <PERSON><PERSON><PERSON><PERSON>hecker extends BasePlatformChecker {
    constructor() {
        super("抖音");
        this.config = PLATFORM_CONFIG["抖音"];
    }

    /**
     * 初始化检查器
     */
    async init() {
        const data = await this.fetchData();
        const hasUpdate = await this.handleDataUpdate(data);
        
        if (hasUpdate) {
            const message = this.createMarkdownMessage(this.name, `阿细的 **抖音** 有新动态！`);
            await this.sendQywx(message);
        }
    }

    /**
     * 获取抖音数据
     * @returns {Promise<object>} 抖音数据
     */
    async fetchData() {
        try {
            const apiUrl = this.config.api + "?device_platform=webapp&aid=6383&channel=channel_pc_web&sec_user_id=MS4wLjABAAAA4iJURTJXsUmAQ9-YzI1PAwmuhDcT08szbjSPil0vygM&max_cursor=0&locate_item_id=7384812466506599690&locate_query=false&show_live_replay_strategy=1&need_time_list=1&time_list_query=0&whale_cut_token=&cut_version=1&count=18&publish_video_strategy_type=2&from_user_page=1&update_version_code=170400&pc_client_type=1&pc_libra_divert=Windows&support_h265=1&support_dash=0&version_code=290100&version_name=29.1.0&cookie_enabled=true&screen_width=1552&screen_height=873&browser_language=zh-CN&browser_platform=Win32&browser_name=Edge&browser_version=*********&browser_online=true&engine_name=Blink&engine_version=*********&os_name=Windows&os_version=10&cpu_core_num=6&device_memory=8&platform=PC&downlink=10&effective_type=4g&round_trip_time=200&webid=7475598730408117786&uifid=3af258ad659545d9553f15cf32bb8a88df248991ebb865c20b5fa6f7dab6eb5462da455c4d5150d8b5a08efea81c1fa2dd7fdc6cc4ddb55081ed93dd999bec79276ab24d95cf93bd70aceb2551d61b2fba257dbfc6a14cae5536831415ce9965dd965c38a2d5f28e574168fe0c31de99&a_bogus=EJ4jDq7yxp5RKdFGmcGrC9%2FUpIolNTSy9tTxWx-THOu9T7lGvYPpENawcxu24uKTOYBswCIHkDeAbdnc%2F0X0ZFHkFmpkuBUymGQII0so%2Fq7VbBvgV1b8eJbEKi-FUWsPO%2FdAinEXU0lrIocfZHK2AFF9CAto58m8sre-dPWlHxg-g-JYVxd4eahP&verifyFp=verify_m7lhmmkp_5fZqgaEO_trUZ_4ott_BLeo_7Ab8NnjAGBut&fp=verify_m7lhmmkp_5fZqgaEO_trUZ_4ott_BLeo_7Ab8NnjAGBut";
            
            const response = await fetch(apiUrl, {
                "headers": {
                    "accept": "application/json, text/plain, */*",
                    "accept-language": "zh-CN,zh;q=0.9",
                    "cache-control": "no-cache",
                    "pragma": "no-cache",
                    "priority": "u=1, i",
                    "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Microsoft Edge\";v=\"133\", \"Chromium\";v=\"133\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "uifid": "3af258ad659545d9553f15cf32bb8a88df248991ebb865c20b5fa6f7dab6eb5462da455c4d5150d8b5a08efea81c1fa2dd7fdc6cc4ddb55081ed93dd999bec79276ab24d95cf93bd70aceb2551d61b2fba257dbfc6a14cae5536831415ce9965dd965c38a2d5f28e574168fe0c31de99",
                    "cookie": "__ac_nonce=067bea982007ba1ff211f; __ac_signature=_02B4Z6wo00f01Gw8YqwAAIDCiTVbXCvb3yhsHGYAAHy5ba; ttwid=1%7CW9m1sV9VuMyw4M2MBklOwIvs_1gyZIGFxKxiICMnIFo%7C1740548482%7C086de15b244896c9309053eed81bc18053836bd94ac0c7b9f7d3b1f91469f68a; UIFID_TEMP=3af258ad659545d9553f15cf32bb8a88df248991ebb865c20b5fa6f7dab6eb5462da455c4d5150d8b5a08efea81c1fa2dd7fdc6cc4ddb55081ed93dd999bec79276ab24d95cf93bd70aceb2551d61b2fba257dbfc6a14cae5536831415ce9965dd965c38a2d5f28e574168fe0c31de99; hevc_supported=true; IsDouyinActive=true; home_can_add_dy_2_desktop=%220%22; dy_swidth=1552; dy_sheight=873; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1552%2C%5C%22screen_height%5C%22%3A873%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A6%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A200%7D%22; csrf_session_id=dd42e9883c30dce5340b02ed663de029",
                    "Referer": "https://www.douyin.com/user/MS4wLjABAAAA4iJURTJXsUmAQ9-YzI1PAwmuhDcT08szbjSPil0vygM?vid=7384812466506599690",
                    "Referrer-Policy": "strict-origin-when-cross-origin",
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
                },
                "body": null,
                "method": "GET"
            });
            
            const res = await response.json();
            const result = res.aweme_list[3];
            
            const data = {
                [this.name]: formatTimestamp(result.create_time)
            };
            
            this.logSuccessfulFetch(data);
            return data;
        } catch (error) {
            await this.handleApiError(error, '抖音API');
            return {};
        }
    }
}

/**
 * 抖音直播检查器类
 */
export class DouyinLiveChecker extends BasePlatformChecker {
    constructor() {
        super("抖音直播");
        this.config = PLATFORM_CONFIG["抖音直播"];
    }

    /**
     * 初始化检查器
     */
    async init() {
        const data = await this.fetchData();
        const shouldNotify = await this.handleLiveStatusUpdate(data, 2);
        
        if (shouldNotify) {
            const message = this.createNewsMessage(
                this.name,
                `阿细在抖音直播啦，快来围观！`,
                this.config.url,
                "https://api.960818.xyz/img/ax/index.php"
            );
            await this.sendQywx(message);
        }
    }

    /**
     * 获取抖音直播数据
     * @returns {Promise<object>} 抖音直播数据
     */
    async fetchData() {
        try {
            const apiUrl = this.config.api + "?aid=6383&app_name=douyin_web&live_id=1&device_platform=web&language=zh-CN&enter_from=page_refresh&cookie_enabled=true&screen_width=1552&screen_height=873&browser_language=zh-CN&browser_platform=Win32&browser_name=Edge&browser_version=*********&web_rid=713643052823&room_id_str=7470146013328624418&enter_source=&is_need_double_stream=false&insert_task_id=&live_reason=&a_bogus=dj4nktXwEZ%2FRad%2FGYKG1Cn5Uf0ylNsSy3sixSHVuHNPAT7zTLSPxgOCznxu14KAiaRBsioVHBDelYxdcQIXw1ZHkzmkvSFift025VX0L2qwXaUikgHDDehDFKwBC8mJwe%2F99ilf5Xs0N1VdAINV%2FAdlGH5FH-bEdSrpep%2FEynDc03B6TV92ICrYWUwD%3D";
            
            const response = await fetch(apiUrl, {
                "headers": {
                    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                    "cache-control": "no-cache",
                    "pragma": "no-cache",
                    "priority": "u=0, i",
                    "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Microsoft Edge\";v=\"133\", \"Chromium\";v=\"133\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "document",
                    "sec-fetch-mode": "navigate",
                    "sec-fetch-site": "none",
                    "sec-fetch-user": "?1",
                    "sec-gpc": "1",
                    "upgrade-insecure-requests": "1",
                    "cookie": "xgplayer_user_id=548922642316; live_use_vvc=%22false%22; hevc_supported=true; UIFID_TEMP=c3109cf8eab4507640f022360c5ce002c7035d0857c7085fdeb180d1661fca19c4e53e0d34762335ad1c35bb6b953d9375b2c04a7ae6bebc31fca630c2289a92b58609360c4f8dc899db6a1d5f4ae9ac96b57d48d07514364cafae671f43305807c831b9b0aabd96d0d2389d19b4ac3e; UIFID=c3109cf8eab4507640f022360c5ce002c7035d0857c7085fdeb180d1661fca19b7605030b444423ff3525c68b7a4b37273864e9e0b643eeae627c95c699a4656b94b7ce2bbef2a455e234961cad69dc3bc6c7e2c8d1f1bd067f79db80f91c2750a9038d3bb1132668b4d5e0243f86315325212955c90d014394a5bb1d2b29cbb2139b48338621d0e815bae9f699e9b86f2db326b15b39fe5e2e5f726f41c8d41; bd_ticket_guard_client_web_domain=2; d_ticket=6633613373be238632143c78eb86ec8c033d5; passport_assist_user=Cj2_gpELHbGlehPNLKg6M0qPJ6iZDWtSWZ8UjmE5SDAEAbqv0bhSLZnD9_BZn84polzAtvd8DZzpMDNMRrBdGkoKPOgnoNz-cm4XHKS2qfdveCAU1o5p1s0rl-yR7s-6ZKJ6ImT5un4ImKbQvtOBkpdtOW08sWgbRpvw-4sUrRDU3N0NGImv1lQgASIBA-ymVM4%3D; uid_tt=7e96da6ea85d2440106d3cb275f774bd; uid_tt_ss=7e96da6ea85d2440106d3cb275f774bd; sid_tt=d67ddd9c174cb877d62354905ab4006d; sessionid=d67ddd9c174cb877d62354905ab4006d; sessionid_ss=d67ddd9c174cb877d62354905ab4006d; is_staff_user=false; _bd_ticket_crypt_cookie=47d00ded0a068e2f0de4d1812ca16fea; store-region=cn-gd; store-region-src=uid; my_rd=2; fpk1=U2FsdGVkX1+ymz03MosiwCj5ebO7sTgH4iKjoQgDYJsstClbYiyQlZ5VweohI1zWC8mVva2+ijP3fXetSW8Vmw==; fpk2=e2dd8fac9c214f2e57aa0dea655ff030; ttwid=1%7C4Wp8fqI8tXNcDlNdR8SO0B-EvUF8ksuggm8C-KkmtaA%7C1735652835%7Cd8d1e9bda8e60efedd93f778884301a91ec959ed119c8956808d11362b71f00a"
                },
                "referrerPolicy": "strict-origin-when-cross-origin",
                "body": null,
                "method": "GET"
            });
            
            const result = await response.json();
            
            if (!result.data.data[0].id_str) {
                console.log(`${this.logTime}: ${this.name}通过api获取数据失败，估计被限制了`);
                return {};
            }
            
            const data = {
                [this.name]: result.data.data[0].status,
            };
            
            console.log(`通过抖音API获取抖音直播数据结果：${JSON.stringify(data)}`);
            return data;
        } catch (error) {
            console.log(`${this.name}通过api获取数据失败`);
            const message = {
                "msgtype": "text",
                "text": {
                    "content": `${this.name}通过api获取数据失败`
                }
            };
            await this.sendQywx(message);
            return {};
        }
    }
}
