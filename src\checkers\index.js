/**
 * 平台检查器索引文件
 */

import { InstagramChecker } from './instagram.js';
import { <PERSON>hongshuChe<PERSON> } from './xiaohongshu.js';
import { BilibiliChecker, BilibiliLiveChecker } from './bilibili.js';
import { YoutubeChecker } from './youtube.js';
import { WeiboChecker } from './weibo.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DouyinLiveChecker } from './douyin.js';
import { JrttChecker } from './jrtt.js';

/**
 * 平台检查器映射表
 */
export const PLATFORM_CHECKERS = {
    "instagram": InstagramChecker,
    "xiaohongshu": <PERSON>hongshuChe<PERSON>,
    "douyin": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    "weibo": WeiboChecker,
    "bilibili": BilibiliChecker,
    "youtube": YoutubeChecker,
    "jrtt": <PERSON><PERSON><PERSON><PERSON><PERSON>,
    "douyinlive": <PERSON><PERSON>inLiveChecker,
    "bilibili_live": BilibiliLiveChecker,
};

/**
 * 获取平台检查器
 * @param {string} platform - 平台名称
 * @returns {BasePlatformChecker} 平台检查器实例
 */
export function getPlatformChecker(platform) {
    const CheckerClass = PLATFORM_CHECKERS[platform];
    if (!CheckerClass) {
        throw new Error(`不支持的平台: ${platform}`);
    }
    return new CheckerClass();
}

/**
 * 获取所有支持的平台列表
 * @returns {Array<string>} 平台列表
 */
export function getSupportedPlatforms() {
    return Object.keys(PLATFORM_CHECKERS);
}
