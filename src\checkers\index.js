/**
 * 平台检查器索引文件
 */

import { InstagramChecker } from './instagram.js';
import { XiaohongshuChe<PERSON> } from './xiaohongshu.js';
import { BilibiliChecker, BilibiliLiveChecker } from './bilibili.js';
import { YoutubeChecker } from './youtube.js';
import { WeiboChecker } from './weibo.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DouyinLiveChecker } from './douyin.js';
import { JrttChecker } from './jrtt.js';

/**
 * 平台检查器映射表
 */
export const PLATFORM_CHECKERS = {
    "Instagram": InstagramChecker,
    "小红书": XiaohongshuChecker,
    "抖音": <PERSON><PERSON>inChe<PERSON>,
    "微博": WeiboChecker,
    "哔哩哔哩": BilibiliChecker,
    "YouTube": YoutubeChecker,
    "今日头条": JrttChecker,
    "抖音直播": DouyinLiveChecker,
    "哔哩哔哩直播": BilibiliLiveChecker,
};

/**
 * 获取平台检查器
 * @param {string} platform - 平台名称
 * @returns {BasePlatformChecker} 平台检查器实例
 */
export function getPlatformChecker(platform) {
    const CheckerClass = PLATFORM_CHECKERS[platform];
    if (!CheckerClass) {
        throw new Error(`不支持的平台: ${platform}`);
    }
    return new CheckerClass();
}

/**
 * 获取所有支持的平台列表
 * @returns {Array<string>} 平台列表
 */
export function getSupportedPlatforms() {
    return Object.keys(PLATFORM_CHECKERS);
}
