# 阿细社交媒体监控系统

重构后的模块化社交媒体监控系统，支持多平台内容监控和通知推送。

## 系统架构

### 重构前后对比

**重构前：**
- 单一 `main.js` 文件（874行）
- 所有功能混合在一起
- 代码重复，难以维护
- 硬编码配置

**重构后：**
- 模块化架构，职责分离
- 可扩展的平台检查器系统
- 统一的配置管理
- 清晰的服务层架构

### 目录结构

```
ax-check/
├── main.js                    # 主程序入口
├── src/
│   ├── config/
│   │   └── index.js          # 配置管理
│   ├── utils/
│   │   └── time.js           # 时间工具
│   ├── services/
│   │   ├── notification.js   # 通知服务
│   │   ├── storage.js        # 存储服务
│   │   ├── app.js           # 应用服务
│   │   └── server.js        # HTTP服务器
│   └── checkers/
│       ├── base.js          # 基础检查器类
│       ├── index.js         # 检查器工厂
│       ├── instagram.js     # Instagram检查器
│       ├── xiaohongshu.js   # 小红书检查器
│       ├── douyin.js        # 抖音检查器
│       ├── weibo.js         # 微博检查器
│       ├── bilibili.js      # 哔哩哔哩检查器
│       ├── youtube.js       # YouTube检查器
│       ├── jrtt.js          # 今日头条检查器
│       ├── douyinlive.js    # 抖音直播检查器
│       └── bilibili_live.js # 哔哩哔哩直播检查器
├── test.js                   # 系统测试
└── README.md                # 项目文档
```

## 核心模块

### 1. 配置管理 (`src/config/index.js`)
- 集中管理所有配置项
- 支持测试模式切换
- 平台配置和通知配置

### 2. 通知服务 (`src/services/notification.js`)
- 企业微信机器人通知
- wxpusher推送
- 息知通知
- 统一的消息格式处理

### 3. 存储服务 (`src/services/storage.js`)
- 数据持久化（内存存储）
- 数据对比功能
- 直播状态检查

### 4. 平台检查器 (`src/checkers/`)
- 基于继承的检查器架构
- 每个平台独立的检查逻辑
- 统一的错误处理

### 5. 应用服务 (`src/services/app.js`)
- 定时监控管理
- 平台检查协调
- 数据报告生成

### 6. HTTP服务器 (`src/services/server.js`)
- RESTful API接口
- 手动检查触发
- 健康检查端点

## 支持的平台

- Instagram
- 小红书 (xiaohongshu)
- 抖音 (douyin)
- 微博 (weibo)
- 哔哩哔哩 (bilibili)
- YouTube
- 今日头条 (jrtt)
- 抖音直播 (douyinlive)
- 哔哩哔哩直播 (bilibili_live)

## API 接口

### 健康检查
```
GET /health
```

### 获取数据报告
```
GET /data
```

### 手动检查平台
```
GET /{platform_name}
```

### 删除所有数据
```
GET /delete
```

## 运行方式

### 启动系统
```bash
deno run --allow-all main.js
```

### 运行测试
```bash
deno run --allow-all test.js
```

## 特性

- ✅ 模块化架构，易于维护和扩展
- ✅ 统一的错误处理和日志记录
- ✅ 支持定时监控（每4分钟）
- ✅ HTTP API接口支持手动触发
- ✅ 多种通知方式支持
- ✅ 配置集中管理
- ✅ 内存数据存储
- ✅ 随机延迟防止API限制

## 技术栈

- **运行时**: Deno
- **语言**: JavaScript (ES6 modules)
- **HTTP服务**: Deno.serve
- **XML解析**: jsr:@libs/xml
- **存储**: 内存存储 (Map)

## 重构优势

1. **可维护性**: 模块化设计，职责分离
2. **可扩展性**: 新增平台只需添加对应检查器
3. **可测试性**: 独立模块便于单元测试
4. **可配置性**: 集中配置管理
5. **错误处理**: 统一的错误处理机制
6. **代码复用**: 基础类和工具函数复用
