/**
 * Cloudflare Workers KV 存储适配器
 */
import { BaseStorageAdapter } from './base.js';

export class CloudflareKVAdapter extends BaseStorageAdapter {
    constructor(namespace = null) {
        super();
        // Cloudflare Workers中，KV namespace通常通过环境变量绑定
        // 例如: this.kv = MY_KV_NAMESPACE;
        this.kv = namespace || (typeof MY_KV_NAMESPACE !== 'undefined' ? MY_KV_NAMESPACE : null);
        this.isInitialized = false;
    }

    /**
     * 初始化Cloudflare KV存储
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async init() {
        try {
            // 检查KV namespace是否可用
            if (!this.kv) {
                // 尝试从全局变量获取
                if (typeof MY_KV_NAMESPACE !== 'undefined') {
                    this.kv = MY_KV_NAMESPACE;
                } else {
                    console.error('Cloudflare KV namespace未找到');
                    return false;
                }
            }

            // 测试KV是否可用
            await this.kv.get('__test__');
            this.isInitialized = true;
            console.log('Cloudflare KV存储初始化成功');
            return true;
        } catch (error) {
            console.error('Cloudflare KV存储初始化失败:', error);
            this.isInitialized = false;
            return false;
        }
    }

    /**
     * 检查存储是否可用
     * @returns {Promise<boolean>} 是否可用
     */
    async isAvailable() {
        if (!this.isInitialized) {
            return await this.init();
        }
        return this.kv !== null;
    }

    /**
     * 将键数组转换为字符串键
     * @param {Array|string} key - 键
     * @returns {string} 字符串键
     */
    normalizeKey(key) {
        return Array.isArray(key) ? key.join(':') : key;
    }

    /**
     * 获取数据
     * @param {Array|string} key - 键
     * @returns {Promise<{value: any}>} 数据值
     */
    async get(key) {
        if (!await this.isAvailable()) {
            throw new Error('Cloudflare KV存储不可用');
        }

        const normalizedKey = this.normalizeKey(key);
        const value = await this.kv.get(normalizedKey, 'json');
        return { value: value || null };
    }

    /**
     * 设置数据
     * @param {Array|string} key - 键
     * @param {any} value - 值
     * @returns {Promise<boolean>} 设置是否成功
     */
    async set(key, value) {
        if (!await this.isAvailable()) {
            throw new Error('Cloudflare KV存储不可用');
        }

        const normalizedKey = this.normalizeKey(key);
        await this.kv.put(normalizedKey, JSON.stringify(value));
        return true;
    }

    /**
     * 删除数据
     * @param {Array|string} key - 键
     * @returns {Promise<boolean>} 删除是否成功
     */
    async delete(key) {
        if (!await this.isAvailable()) {
            throw new Error('Cloudflare KV存储不可用');
        }

        const normalizedKey = this.normalizeKey(key);
        await this.kv.delete(normalizedKey);
        return true;
    }

    /**
     * 获取存储类型名称
     * @returns {string} 存储类型
     */
    getType() {
        return 'Cloudflare KV';
    }

    /**
     * 清理资源
     * @returns {Promise<void>}
     */
    async cleanup() {
        // Cloudflare KV不需要显式清理
        this.isInitialized = false;
    }
}
